# Branch Protection Rules Setup

This document outlines the branch protection rules that should be configured for this repository to ensure code quality and testing requirements.

## 🔒 Required Branch Protection Settings

### For `feature/setup`branche:

1. **Require pull request reviews before merging**
   - Required number of reviewers: 1
   - Dismiss stale reviews when new commits are pushed: ✅
   - Require review from code owners: ✅ (optional)

2. **Require status checks to pass before merging**
   - Require branches to be up to date before merging: ✅
   - **Required status checks (CRITICAL):**
     - `Backend Tests & Coverage`
     - `Frontend Tests & Coverage`
     - `Coverage Gate`

3. **Require conversation resolution before merging**: ✅

4. **Include administrators**: ✅ (administrators must also follow these rules)

5. **Restrict pushes that create files that exceed the path length limit**: ✅

## 📊 Coverage Requirements

- **Minimum 80% test coverage** for both frontend and backend
- All tests must pass
- Code formatting checks must pass (go fmt, ESLint)
- No linting errors allowed

## ⚙️ How to Configure Branch Protection

1. Go to your repository on GitHub
2. Navigate to **Settings** → **Branches**
3. Click **Add rule** for `main` branch
4. Configure the following settings:
   - ✅ Require a pull request before merging
   - ✅ Require approvals (set to 1)
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - Add these required status checks:
     - `Backend Tests & Coverage`
     - `Frontend Tests & Coverage`
     - `Coverage Gate`
   - ✅ Require conversation resolution before merging
   - ✅ Include administrators
5. Repeat for `develop` branch

## 🧪 Testing Commands (for local development)

### Backend
```bash
cd backend
go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
go tool cover -func=coverage.out
```

### Frontend
```bash
cd frontend
npm run test:coverage
```

## 📈 Coverage Thresholds

Both frontend and backend must maintain **80% minimum coverage** for:
- Lines: 80%
- Functions: 80%
- Branches: 80%
- Statements: 80%

## 🚫 What Happens When Requirements Aren't Met

- **Tests fail**: PR cannot be merged
- **Coverage below 80%**: PR cannot be merged
- **Linting errors**: PR cannot be merged
- **Formatting issues**: PR cannot be merged

The GitHub Actions workflow will automatically:
- ❌ Block the PR if any requirement fails
- ✅ Allow merging only when all requirements pass
- 💬 Comment on the PR with detailed status

## 🎯 Benefits

- Ensures code quality and reliability
- Prevents bugs from reaching production
- Maintains consistent code style
- Provides confidence in deployments
- Encourages comprehensive testing
