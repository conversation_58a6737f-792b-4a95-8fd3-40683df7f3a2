## Description

Brief summary of changes and the motivation behind them.

### Related Issue
Closes #(issue_number)

---

## Type of Change

Please delete options that are not relevant:

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Configuration change
- [ ] Other (please describe):

## Changes Made

- List the specific changes made in this PR
- Be as detailed as necessary
- Include any architectural decisions or trade-offs


## Screenshots/Videos (if applicable)

Add screenshots or videos to help explain your changes if they affect the UI.

## Checklist

- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published


## Additional Notes

Any additional information that reviewers should know about this PR.
