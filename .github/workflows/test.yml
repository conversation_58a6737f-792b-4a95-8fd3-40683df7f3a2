name: Run Tests

on:
  pull_request:
    branches: [ feature/setup ]
  push:
    branches: [ feature/setup ]

jobs:
  backend-tests:
    name: Backend Tests (Go)
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'

    - name: Run go vet
      run: go vet ./...
      working-directory: ./backend

    - name: Run go fmt check
      run: |
        unformatted=$(gofmt -s -l .)
        if [ -n "$unformatted" ]; then
          echo "The following files are not formatted:"
          echo "$unformatted"
          exit 1
        fi
      working-directory: ./backend

  frontend-tests:
    name: Frontend Tests (React + Vite)
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '24.4.1'
        cache: 'npm'
        cache-dependency-path: './frontend/package-lock.json'

    - name: Install dependencies
      run: npm install
      working-directory: ./frontend

    - name: Run ESLint
      run: npm run lint
      working-directory: ./frontend