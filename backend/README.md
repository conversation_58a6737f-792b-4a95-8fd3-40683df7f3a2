# Backend Structure

This backend directory is structured for scalability, maintainability, and clarity following the principles of **Clean Architecture** (also known as Layered Architecture). The core idea is the **Dependency Rule**: source code dependencies can only point inwards. The outer layers know about the inner layers, but the inner layers know nothing about the outer ones. This is achieved through the use of interfaces.

---

## Architecture & Data Flow

Our architecture is divided into distinct layers, each with a single responsibility. This separation of concerns makes the Application easier to test, maintain, and scale.

#### The Layers

1.  **Handler/Transport Layer (`internal/handler`)**: The outermost layer. Its only job is to handle transport-level concerns, such as receiving HTTP requests. It decodes JSON bodies into Go structs and calls the appropriate service method. It knows nothing about business logic or the database.
2.  **Service/Business Logic Layer (`internal/services`)**: This layer contains the core Application logic and orchestrates business processes. It performs validation, authorization, and coordinates with the repository layer to fulfill a use case (e.g., creating a user). It has no knowledge of HTTP or SQL.
3.  **Repository/Data Access Layer (`internal/repository`)**: This is the only layer that is aware of the database. Its responsibility is to execute SQL queries and map the results to and from our domain models. It implements an interface defined by the service layer, but it knows nothing about the business rules themselves.
4.  **Domain/Models Layer (`internal/models`)**: The innermost layer. It contains the core data structures (structs) of our Application, like `User`. These models are plain Go structs and have zero dependencies.

#### Request & Data Flow Example (Creating a User)

When a `POST /healthcheck/user` request arrives, the data flows as follows:

1.  **`cmd/api/main.go`**: The server's entry point receives the request and the router passes it to the `HealthCheckHandler`. The dependencies (like the user service) have already been injected into the handler via the `Application` struct.
2.  **`handler.HealthCheckHandler`**:
    -   Receives the raw `http.Request`.
    -   Decodes the JSON body into a local `input` struct.
    -   Calls the service layer: `app.TestuserService.CreateTestUser(...)`.
3.  **`services.CreateTestUser`**:
    -   Receives primitive data (`name`, `email`).
    -   Performs business logic (trims whitespace, checks for empty values).
    -   Constructs a `models.User` struct.
    -   Calls the repository interface to persist the user: `s.repo.Create(user)`.
4.  **`repository.Create`**:
    -   Receives the `models.User` struct.
    -   Constructs and executes the `INSERT INTO users ... RETURNING id, created_at` SQL query.
    -   Uses `.Scan()` to populate the `ID` and `CreatedAt` fields back into the user struct.
    -   Returns the complete `user` object (or an error) to the service layer.
5.  **The Return Trip**: The service layer returns the user object to the handler. The handler then encodes this complete object into a JSON response and writes it back to the client.

---

## Database Connection & Migrations

#### Connection to Supabase

The backend connects to our Supabase instance as a standard PostgreSQL database. The Application itself has no specific "Supabase" logic.

-   **Connection String**: The connection is configured via the `DATABASE_URL` environment variable, which is securely stored in the `.env` file (and excluded from Git via `.gitignore`).
-   **Driver**: We use the standard Go `database/sql` package along with the `lib/pq` PostgreSQL driver.
-   **Connection Pooling**: The connection is established once at Application startup in `cmd/api/main.go` and maintained as a connection pool for efficiency. We use the **Transaction Pooler** URL from Supabase, as it is designed for high-concurrency and serverless environments, providing better performance and IPv4 compatibility.

#### Migrations (`migrations/`)

Database schema changes are managed through timestamped SQL migration files.

-   **Tooling**: We use the `golang-migrate/migrate` CLI tool to apply and reverse schema changes.
-   **Execution**: To run migrations against the database, we load the `.env` file to provide the connection string to the CLI:
    ```bash
    # From the backend/ directory
    source .env && migrate -database "$DATABASE_URL" -path ./migrations/ up
    ```

---

## Directory Breakdown

*   **`bin/`**: Contains our compiled Application binaries, ready for deployment.
*   **`cmd/api/`**: Contains the `main.go` entry point for our API. This is where we initialize dependencies (like the DB connection), inject them into our handlers, and start the HTTP server.
*   **`internal/`**: Contains the core logic of our Application, structured into the layers described above. Go's convention prevents other Applications from importing packages from an `internal` directory.
    *   **`▸ models/`**: Holds all the domain models (e.g., `User`, `Job`) used across the app.
    *   **`▸ repository/`**: Handles all database operations. Interfaces and their PostgreSQL-specific implementations live here.
    *   **`▸ services/`**: Contains the core business logic (e.g., user registration, data validation).
*   **`migrations/`**: Contains all raw SQL files for creating and modifying the database schema in a version-controlled way.

This structure allows for a clear separation of concerns, enhances testability, and provides a solid foundation for future development.