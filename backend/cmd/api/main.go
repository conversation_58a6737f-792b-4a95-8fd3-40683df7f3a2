package main

import (
	"flag"
	"log"
	"log/slog"
	"os"
	"sync"
	"time"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"

	"county-project/internal/repository"
	"county-project/internal/services"
	"county-project/mailer"
)

// Config struct to hold the configuration settings for the Application
// For now just the network port that the server will listen on and
// the name of the current operating environment(development, production etc)
// Read these config settings from commandline flags when the Application starts
type config struct {
	port int
	env  string
	db   struct {
		dsn          string
		maxOpenConns int
		maxIdleConns int
		maxIdleTime  time.Duration
	}
	smtp struct {
		host     string
		port     int
		username string
		password string
		sender   string
	}
}

// Will hold the dependencies for our http handlers, helpers,
// and middlewares.
type Application struct {
	config             config
	logger             *slog.Logger
	jobUserService     services.JobUserService
	mailer             *mailer.Mailer
	wg                 sync.WaitGroup
	userProfileService services.UserProfileService
}

func main() {
	// 1. Load configuration from .env file
	err := godotenv.Load(".env")
	if err != nil {
		log.Fatal("Error loading .env file")
	}
	// Declare an instance of the config
	var cfg config

	// Read the value of the port and env command-line flags into the config struct.
	// We default to using the port number 4000 and the environment "development" if no
	// corresponding flags are provided
	flag.IntVar(&cfg.port, "port", 4000, "API server port")
	flag.StringVar(&cfg.env, "env", "development", "Environment (development | staging | production)")

	//Read the DSN value from the db-dsn command-line flag into the config struct.
	//We default to using our development DSN if no flag is provided
	flag.StringVar(&cfg.db.dsn, "db-dsn", os.Getenv("DATABASE_URL"), "PostgreSQL DSN")

	// Read the connection pool settings from command-line flags into the config struct.
	flag.IntVar(&cfg.db.maxOpenConns, "db-max-open-conns", 25, "PostgreSQL max open connections")
	flag.IntVar(&cfg.db.maxIdleConns, "db-max-idle-conns", 25, "PostgreSQL max idle connections")
	flag.DurationVar(&cfg.db.maxIdleTime, "db-max-idle-time", 15*time.Minute, "PostgreSQL max connection idle time")

	// Read the SMTP server configuration settings into the config struct, using the
	// Mailtrap settings as the default values.
	flag.StringVar(&cfg.smtp.host, "smtp-host", "sandbox.smtp.mailtrap.io", "SMTP host")
	flag.IntVar(&cfg.smtp.port, "smtp-port", 25, "SMTP port")
	flag.StringVar(&cfg.smtp.username, "smtp-username", "3d9db13d434b54", "SMTP username")
	flag.StringVar(&cfg.smtp.password, "smtp-password", "6c6fa00d747efe", "SMTP password")
	flag.StringVar(&cfg.smtp.sender, "smtp-sender", os.Getenv("SMTP_SENDER"), "SMTP sender")
	flag.Parse()

	// Initialize a new structured logger which writes log entries to the standard output stream
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	//Call the openDB() to create the connection pool passing in
	//the config struct.
	db, err := openDB(cfg)
	if err != nil {
		logger.Error(err.Error())
		os.Exit(1)
	}

	//Defer a call to db.Close() so that the connection pool is closed
	defer db.Close()

	logger.Info("database connection pool established")

	//debugging
	logger.Info("Initializing mailer with sender", "sender_address", cfg.smtp.sender)

	// Initialize a new Mailer instance using the settings from the command line
	// flags.
	mailer, err := mailer.New(cfg.smtp.host, cfg.smtp.port, cfg.smtp.username, cfg.smtp.password, cfg.smtp.sender)
	if err != nil {
		logger.Error(err.Error())
		os.Exit(1)
	}

	// DEPENDENCY INJECTION
	// Create instances, injecting dependencies from bottom layer to top
	jobUserRepo := repository.NewJobUserRepository(db)
	tokenRepo := repository.NewTokenRepository(db)

	jobUserervice := services.NewJobUserService(jobUserRepo, tokenRepo)

	// Dece UserProfileServicelare an instance of the Application struct, containing the config
	// struct and the logger
	app := &Application{
		config:             cfg,
		logger:             logger,
		jobUserService:     jobUserervice,
		mailer:             mailer,
		wg:                 sync.WaitGroup{},
		userProfileService: services.NewUserProfileService(repository.NewUserProfileRepository(db)),
	}

	//call new server method to start the server
	err = app.serve()
	if err != nil {
		logger.Error(err.Error())
		os.Exit(1)
	}
}
