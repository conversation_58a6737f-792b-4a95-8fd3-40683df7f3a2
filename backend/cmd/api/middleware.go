package main

import (
	"county-project/internal/models"
	"county-project/internal/validator"
	"net/http"
	"strings"
	"sync"
	"time"
)

func (app *Application) authenticate(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Add the "Vary: Authorization" header to the response. This indicates to any
		// caches that the response may vary based on the value of the Authorization
		// header in the request.
		w.Header().Add("Vary", "Authorization")

		// Retrieve the value of the Authorization header from the request. This will
		// return the empty string "" if there is no such header found.
		authorizationHeader := r.Header.Get("Authorization")

		// If there is no Authorization header found, use the contextSetUser() helper
		// that just made to add the AnonymousUser to the request context. Then we
		// call the next handler in the chain and return without executing any of the
		// code below.
		if authorizationHeader == "" {
			r = app.contextSetUser(r, models.AnonymousUser)
			next.ServeHTTP(w, r)
			return
		}

		// Otherwise, we expect the value of the Authorization header to be in the format
		// "Bearer <token>". We try to split this into its constituent parts, and if the
		// header isn't in the expected format we return a 401 Unauthorized response
		// using the invalidAuthenticationTokenResponse() helper (which we will create
		// in a moment).
		headerParts := strings.Split(authorizationHeader, " ")
		if len(headerParts) != 2 || headerParts[0] != "Bearer" {
			app.invalidAuthenticationTokenResponse(w, r)
			return
		}

		// Extract the actual authentication token from the header parts.
		token := headerParts[1]

		// Validate the token to make sure it is in a sensible format.
		v := validator.New()
		// If the token isn't valid, use the invalidAuthenticationTokenResponse()
		// helper to send a response, rather than the failedValidationResponse() helper
		if models.ValidateTokenPlaintext(v, token); !v.Valid() {
			app.invalidAuthenticationTokenResponse(w, r)
			return
		}

		//Retrieve the details of the user associated with the authentication token
		//by calling the service layer to process the request
		//call the invalidAuthenticationTokenResponse() helper if no matching record is found
		user, err := app.jobUserService.GetUserForToken(r.Context(), token)
		if err != nil {
			// If we get an error, we know the user is not authenticated.
			app.invalidAuthenticationTokenResponse(w, r)
			return
		}
		//call the contextsetuser() to add the user information to the
		//request context
		r = app.contextSetUser(r, user)

		//call the next handler in the chain
		next.ServeHTTP(w, r)

	})
}

func (app *Application) rateLimit(next http.Handler) http.Handler {
	//Define a client struct to hold the rate limiter and last seen for
	type client struct {
		limiter  *rate.Limiter
		lastSeen time.Time
	}

	//Declare a mutex and a map to hold the clients IP addresses and rate limiters
	var (
		mu sync.Mutex

		//Update the map so the values are pointers to a client struct
		clients = make(map[string]*client)
	)

	//Launch background goroutine which removes old entries from the clients map once
	//every minute
	go func() {
		time.Sleep(time.Minute)

		// Lock the mutex to prevent any rate limiter checks from happening while
		// the cleanup is taking place.
		mu.Lock()

		//loop through all clients. If they haven't been seen within the last
		//three minutes, delete the corresponding entry from the map.
		for ip, client := range clients {
			if time.Since(client.lastSeen) > 3*time.Minute {
				delete(clients, ip)
			}
		}

		//Unlock the mutex when cleanup is complete
		mu.Unlock()
	}()
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		//Use the realip.FromRequest() function to get the client's IP address
		ip := realip.FromRequest(r)

		//Lock the mutex to prevent being executed concurrently
		mu.Lock()

		// Check to see if the IP address already exists in the map. If it doesn't, then
		// initialize a new rate limiter and add the IP address and limiter to the map.

		_, found := clients[ip]

		if !found {
			//create and add a new client struct to the map if it doesn't
			//already exist
			clients[ip] = &client{limiter: rate.NewLimiter(2, 4)}
		}

		//Update the last seen time for the client
		clients[ip].lastSeen = time.Now()

		// Call the Allow() method on the rate limiter for the current IP address. If
		// the request isn't allowed, unlock the mutex and send a 429 Too Many Requests
		// response, just like before.

		if !clients[ip].limiter.Allow() {
			mu.Unlock()
			app.rateLimitExceededResponse(w, r)
			return
		}

		// Very importantly, unlock the mutex before calling the next handler in the
		// chain.
		mu.Unlock()
		next.ServeHTTP(w, r)

	})
}
