package main

import (
	"errors"
	"net/http"
	"time"

	"county-project/internal/models"
)

// envelope is a custom type alias for JSON responses.
// It wraps top-level objects returned by the API, improving consistency and clarity.
type envelope map[string]interface{}

// ErrRecordNotFound is a sentinel error for when no DB record is found.
var ErrRecordNotFound = errors.New("record not found")

// createUserProfileHandler handles POST /v1/profiles requests.
// It creates a user profile for the authenticated user.
func (app *Application) createUserProfileHandler(w http.ResponseWriter, r *http.Request) {
	// Retrieve the authenticated user from request context (set by middleware).
	user := app.contextGetUser(r)

	// Parse and validate the incoming JSON request body into a UserProfile struct.
	var input models.UserProfile
	if err := app.readJSON(w, r, &input); err != nil {
		app.badRequestResponse(w, r, err)
		return
	}
	// Overwrite the ID to ensure it matches the current user's ID.
	input.ID = user.ID

	// Alternatively, allow the service to auto-assign a UUID.
	// Uncomment this line if ID is meant to be generated instead.
	input.ID = "" // Let service assign UUID

	// Assign timestamps in UTC for consistent database records.
	input.CreatedAt = time.Now().UTC()
	input.UpdatedAt = time.Now().UTC()

	// Delegate profile creation to the service layer (which contains business logic).
	if err := app.userProfileService.CreateProfile(r.Context(), input); err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
	// Respond with the newly created profile wrapped in an envelope.
	//Using a custom type like envelope instead of inline map[string]any{} (or map[string]interface{}) is a design
	//choice that offers several benefits for consistency, readability, and maintainability
	// in very short handlers, or one-off error cases, using map[string]any{} might be fine
	//Using envelope is a small but powerful convention that improves long-term maintainability and makes your API more expressive and standardized.
	app.writeJSON(w, http.StatusCreated, envelope{"profile": input})
}

// getMyUserProfileHandler handles GET /v1/profiles/me.
// It retrieves the authenticated user's profile.
func (app *Application) getMyUserProfileHandler(w http.ResponseWriter, r *http.Request) {
	// Get the current authenticated user from the context.
	user := app.contextGetUser(r)

	profile, err := app.userProfileService.GetProfile(r.Context(), user.ID)
	if err != nil {
		if errors.Is(err, ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		// For any other error, return a 500 Internal Server Error.
		app.serverErrorResponse(w, r, err)
		return
	}
	// Respond with the user's profile wrapped in an envelope.

	app.writeJSON(w, http.StatusOK, envelope{"profile": profile})
}

// PUT /v1/profiles/me
func (app *Application) updateMyUserProfileHandler(w http.ResponseWriter, r *http.Request) {
	user := app.contextGetUser(r)

	var input models.UserProfile
	if err := app.readJSON(w, r, &input); err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	input.ID = user.ID
	input.UpdatedAt = time.Now().UTC()

	if err := app.userProfileService.UpdateProfile(r.Context(), input); err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	app.writeJSON(w, http.StatusOK, envelope{"profile": input})
}
