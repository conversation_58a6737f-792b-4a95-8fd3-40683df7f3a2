package main

import (
	"net/http"

	"github.com/julienschmidt/httprouter"
)

func (app *Application) Routes() http.Handler {
	router := httprouter.New()
	router.HandlerFunc(http.MethodPost, "/jobuser/create", app.CreateJobUserHandler)
	router.HandlerFunc(http.MethodPost, "/jobuser/authenticate", app.createAuthenticationTokenHandler)
	router.Handler(http.MethodPost, "/jobuser/logout", app.authenticate(http.HandlerFunc(app.logoutHandler)))

	// User profile routes
	router.Handler(http.MethodPost, "/v1/profiles", app.authenticate(http.HandlerFunc(app.createUserProfileHandler)))
	router.Handler(http.MethodGet, "/v1/profiles/me", app.authenticate(http.HandlerFunc(app.getMyUserProfileHandler)))
	router.Handler(http.MethodPut, "/v1/profiles/me", app.authenticate(http.HandlerFunc(app.updateMyUserProfileHandler)))

	//testing
	router.HandlerFunc(http.MethodGet, "/slow", app.slowHandler)
	return app.rateLimit(router)
}
