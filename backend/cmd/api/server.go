package main

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/rs/cors"
)

func (app *Application) serve() error {
	// basic cors configuration
	c := cors.New(cors.Options{
		// AllowedOrigins specifies which origins are allowed to make cross-site requests.
		//we allow Vite frontend for dev
		AllowedOrigins: []string{"http://localhost:5173"},

		// AllowedMethods specifies which HTTP methods are allowed.
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},

		// AllowedHeaders specifies which headers the client is allowed to send.
		AllowedHeaders: []string{"Content-Type", "Authorization"},

		// Allow credentials like cookies
		AllowCredentials: true,

		// For debugging
		Debug: true,
	})

	// we wrap our existing router with the CORS handler.
	handler := c.<PERSON>ler(app.Routes())

	//Start the HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", app.config.port),
		Handler:      handler,
		IdleTimeout:  time.Minute,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 10 * time.Second,
		ErrorLog:     slog.NewLogLogger(app.logger.Handler(), slog.LevelError),
	}

	//implementation of graceful shutdown
	//shutdownError channel to receive any errors returned
	//by the graceful shutdown() function
	shutdownError := make(chan error)
	//begin a background goroutine
	go func() {
		//Create a quit channel which carries os.Signal values
		quit := make(chan os.Signal, 1)

		//we use signal.Notify() to listen for upcoming SIGINT and SIGTERM  signals
		//and relay them to quit channel. Any other signal will not be caught by
		//signal.Notify() and will return their default behaviour
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

		//Read the signal from the quit channel.This code will block until a signal is received
		s := <-quit

		//log a message to say that the signal has been caught.
		//call the String() method on the signal to get the signal name and log it
		app.logger.Info("caught signal", "signal", s.String())

		//Create a context with a 38-second timeout
		ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
		defer cancel()

		//call shutdown() on the created server, passing in the context just made
		//shutdown() return nil if the graceful shutdown was successful or
		//an error(i.e problem closing the listeners or if the shutdown didn't complete
		//be4 the 30-second context deadline is hit)
		//relay the return value to the shutdownError channel
		shutdownError <- server.Shutdown(ctx)

		// //Exit the application with a 0 success status code
		// os.Exit(0)
	}()

	//log starting server message
	app.logger.Info(">>Starting server", "addr", server.Addr, "env", app.config.env)
	// Calling Shutdown() on our server will cause ListenAndServe() to immediately
	// return a http.ErrServerClosed error. So if we see this error, it is actually a
	// good thing and an indication that the graceful shutdown has started. So we check
	// specifically for this, only returning the error if it is NOT http.ErrServerClosed.
	err := server.ListenAndServe()
	if !errors.Is(err, http.ErrServerClosed) {
		return err
	}
	// Otherwise, we wait to receive the return value from Shutdown() on the
	// shutdownError channel. If return value is an error, we know that there was a
	// problem with the graceful shutdown and we return the error.
	err = <-shutdownError
	if err != nil {

		return err
	}

	app.logger.Info("completing background tasks...")
	// Wait for all background goroutines to finish.
	app.wg.Wait()

	// At this point we know that the graceful shutdown completed successfully and we
	// log a "stopped server" message.
	app.logger.Info("stopped server", "addr", server.Addr)
	return nil
}
