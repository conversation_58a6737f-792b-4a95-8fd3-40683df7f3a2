package main

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"county-project/internal/models"
	"county-project/internal/repository"
	"county-project/internal/validator"
)

var ErrDuplicateEmail = errors.New("email already exist")

func (app *Application) CreateJobUserHandler(w http.ResponseWriter, r *http.Request) {
	var input models.UserInput
	//read raw json to DTO
	err := app.readJSON(w, r, &input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	fmt.Println("step 1")
	//call the service layer to apply business logic regarding creation of a user into the DB
	user, err := app.jobUserService.CreateJobUserService(r.Context(), &input)
	if err != nil {
		v := validator.New()
		// Create a variable to hold the validation error.
		var validationErr *validator.ValidationError
		switch {
		case errors.As(err, &validationErr):
			app.failedValidationResponse(w, r, validationErr.Errors)
		case errors.Is(err, ErrDuplicateEmail):
			v.AddError("email", "a user with this email address already exists")
			app.failedValidationResponse(w, r, v.Errors)
		case errors.Is(err, repository.ErrDuplicateNationalID):
			v.AddError("national_id", "a user with this national ID already exists")
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}
	app.writeJSON(w, http.StatusCreated, user)

	// Use a helper to launch the email sending in a background goroutine.
	app.background(func() {
		// This code will run in the background. If it fails, it will be logged,
		// but it won't affect the user's response.
		err = app.mailer.Send(user.Email, "user_welcome.html", user)
		if err != nil {
			app.logError(r, err)
		}
	})

}

// Reads incoming json containing login credentials then authenticates user
func (app *Application) createAuthenticationTokenHandler(w http.ResponseWriter, r *http.Request) {
	var input *models.LoginInput
	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	//call the service layer to validate the input data and generate token
	token, err := app.jobUserService.Authenticate(r.Context(), input)
	if err != nil {
		var validationErr *validator.ValidationError

		switch {
		// If the service returned a validation error
		case errors.As(err, &validationErr):
			app.failedValidationResponse(w, r, validationErr.Errors)

		// If the service returned an invalid credentials error
		case errors.Is(err, repository.ErrInvalidCredentials):
			app.invalidCredentialsResponse(w, r)

		// For any other kind of error, it's an unexpected server problem.
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Encode the token to JSON and send it in the response along with a 201 Created
	// status code.
	err = app.writeJSON(w, http.StatusCreated, map[string]any{"authentication_token": token})
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}

}

// logoutHandler handles user logout by invalidating the session token provided in the Authorization header.
func (app *Application) logoutHandler(w http.ResponseWriter, r *http.Request) {
	// Step 1: Extract the Authorization header from the incoming request.
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		// If the header is missing, return a 401 Unauthorized response.
		app.invalidAuthenticationTokenResponse(w, r)
		return
	}

	// Step 2: The expected format is "Bearer <token>". Split the header into parts.
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		// If the format is invalid, return a 400 Bad Request response.
		http.Error(w, "Invalid Authorization header format", http.StatusBadRequest)
		return
	}

	// Step 3: Extract the actual token string from the header.
	token := parts[1]

	// Step 4: Call the logout method from the auth service to invalidate the token.
	err := app.jobUserService.Logout(r.Context(), token)
	if err != nil {
		// // If logout fails (e.g., token not found), return a 500 Internal Server Error.
		// http.Error(w, "Logout failed: "+err.Error(), http.StatusInternalServerError)
		// return

		var validationErr *validator.ValidationError
		switch {
		case errors.As(err, &validationErr):
			app.failedValidationResponse(w, r, validationErr.Errors)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	// Step 5: Return a JSON response indicating successful logout.
	// w.Header().Set("Content-Type", "application/json")
	// w.WriteHeader(http.StatusOK)
	// json.NewEncoder(w).Encode(map[string]string{"message": "Successfully logged out"})

	err = app.writeJSON(w, http.StatusOK, map[string]string{"message": "successfully logged out"})
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// The background() helper accepts an arbitrary function as a parameter.
func (app *Application) background(fn func()) {
	//Increment the WaitGroup counter
	app.wg.Add(1)
	// Launch a background goroutine.
	go func() {
		//we use defer to decrement the waitGroup counter before the goroutine returns
		defer app.wg.Done()
		// Recover any panic.
		defer func() {
			if err := recover(); err != nil {
				app.logger.Error(fmt.Sprintf("%v", err))
			}
		}()

		// Execute the arbitrary function that we passed as the parameter.
		fn()
	}()
}

func (app *Application) slowHandler(w http.ResponseWriter, r *http.Request) {
	app.logger.Info("Starting slow request...")

	// Simulate a 10-second long task.
	// time.Sleep blocks the handler from finishing.
	time.Sleep(20 * time.Second)

	app.logger.Info("...finished slow request.")

	// Once the sleep is over, send a response.
	fmt.Fprintln(w, "Finally, I'm done.")
}
