package models

import (
	"fmt"
	"net/url"
	"regexp"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"

	// "county-project/internal/models"
	// "county-project/internal/models"
	"county-project/internal/validator"
)

// anonymous user variable sent to requests with no Authorization header
// AnonymousUser variable, which holds a pointer to a User struct
// representing an inactivated user with no ID, name, email or password.
var AnonymousUser = &User{}

type User struct {
	ID          string    `json:"id"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Email       string    `json:"email"`
	PhoneNumber string    `json:"phone_number"`
	NationalID  string    `json:"national_id"`
	Gender      string    `json:"gender"`
	County      string    `json:"county"`
	CreatedAt   time.Time `json:"created_at"`
	Password    Password  `json:"-"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type UserProfile struct {
	ID                 string    `json:"id"`
	FirstName          string    `json:"first_name"`
	LastName           string    `json:"last_name"`
	NationalID         string    `json:"national_id"`
	Email              string    `json:"email"`
	Phone              string    `json:"phone"`
	Gender             string    `json:"gender"`
	County             string    `json:"county"`
	ProfilePhotoURL    string    `json:"profile_photo_url"`
	Nationality        string    `json:"nationality"`
	DOB                string    `json:"dob"` // You can use time.Time if you're parsing it
	EducationLevel     string    `json:"education_level"`
	WorkExperience     string    `json:"work_experience"`
	Skills             []string  `json:"skills"`
	ResumeURL          string    `json:"resume_url"`
	CoverLetterURL     string    `json:"cover_letter_url"`
	CertificationsURLs []string  `json:"certifications_url"`
	DisabilityStatus   string    `json:"disability_status"`
	Availability       string    `json:"availability"`
	PreferredJobType   string    `json:"preferred_job_type"`
	PreferredLocations []string  `json:"preferred_locations"`
	LinkedInURL        string    `json:"linkedin_url"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// ToUserProfile converts the validated UserProfileInput struct
// into a UserProfile model suitable for database operations.
// It is typically called after input validation in the service layer.
func (input *UserProfile) ToUserProfile() *UserProfile {
	return &UserProfile{
		ID:                 input.ID,
		FirstName:          input.FirstName,
		LastName:           input.LastName,
		NationalID:         input.NationalID,
		Email:              input.Email,
		Phone:              input.Phone,
		Gender:             input.Gender,
		County:             input.County,
		ProfilePhotoURL:    input.ProfilePhotoURL,
		Nationality:        input.Nationality,
		DOB:                input.DOB,
		EducationLevel:     input.EducationLevel,
		WorkExperience:     input.WorkExperience,
		Skills:             input.Skills,
		ResumeURL:          input.ResumeURL,
		CoverLetterURL:     input.CoverLetterURL,
		CertificationsURLs: input.CertificationsURLs,
		DisabilityStatus:   input.DisabilityStatus,
		Availability:       input.Availability,
		PreferredJobType:   input.PreferredJobType,
		PreferredLocations: input.PreferredLocations,
		LinkedInURL:        input.LinkedInURL,
	}
}

// Check if a User instance is the AnonymousUser.
func (u *User) IsAnonymous() bool {
	return u == AnonymousUser
}

// represents raw json data from the client
type UserInput struct {
	FirstName   *string `json:"first_name"`
	LastName    *string `json:"last_name"`
	Email       *string `json:"email"`
	PhoneNumber *string `json:"phone_number"`
	NationalID  *string `json:"national_id"`
	Gender      *string `json:"gender"`
	County      *string `json:"county"`
	Password    *string `json:"password"`
}

// LoginInput holds the email and password from a client's login request.
type LoginInput struct {
	Email    *string `json:"email"`
	Password *string `json:"password"`
}

// Custom password type  is a struct containing the plaintext and hashed
// versions of the password for a user. The plaintext field is a *pointer* to a string,
// so that we're able to distinguish between a plaintext password not being present in
// the struct at all, versus a plaintext password which is the empty string "".
type Password struct {
	Plaintext *string
	Hash      []byte
}

// Set calculates the bcrypt type to manage plaintext and hashed
// passwords
func (p *Password) Set(plaintextPassword string) (*Password, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(plaintextPassword), 12)
	if err != nil {
		return nil, err
	}
	p.Plaintext = &plaintextPassword
	p.Hash = hash

	fmt.Println("plaintext:", p.Plaintext)
	fmt.Println("plaintext:", p.Hash)
	return p, nil
}

// Matches checks if the provided plaintext password matches
// the stored hashed password.
func (p *Password) Matches(plaintextPassword string) (bool, error) {
	err := bcrypt.CompareHashAndPassword(p.Hash, []byte(plaintextPassword))
	if err != nil {
		switch {
		case err == bcrypt.ErrMismatchedHashAndPassword:
			return false, nil
		default:
			return false, err
		}
	}
	return true, nil
}

// Check that the Email field is not the empty string, and that it matches the regular
// expression for email addresses that we added in our validator package earlier in the
// book.
func ValidateEmail(v *validator.Validator, email string) {
	v.Check(email != "", "email", "must be provided")
	v.Check(validator.Matches(email, validator.EmailRX), "email", "must be a valid email address")
}

// Check if the Password.plaintext field is not nil , then check that the value is not the empty
// string and is between 8 and 72 bytes long.
func ValidatePasswordPlaintext(v *validator.Validator, password string) {
	v.Check(password != "", "password", "must be provided")
	v.Check(len(password) >= 8, "password", "must be at least 8 bytes long")
	v.Check(len(password) <= 72, "password", "must not be more than 72 bytes long")
}
func ValidateUser(v *validator.Validator, user *User) {
	v.Check(user.FirstName != "", "name", "must be provided")
	v.Check(len(user.LastName) <= 500, "name", "must not be more than 500 bytes long")
	// Call the standalone ValidateEmail() helper.
	ValidateEmail(v, user.Email)
	// If the plaintext password is not nil, call the standalone
	// ValidatePasswordPlaintext() helper.
	if user.Password.Plaintext != nil {
		ValidatePasswordPlaintext(v, *user.Password.Plaintext)
	}

	v.Check(user.LastName != "", "last_name", "must be provided")
	v.Check(user.PhoneNumber != "", "phone_number", "must be provided")
	v.Check(user.NationalID != "", "national_id", "must be provided")

	// regex for validating phone format
	// Safaricom format, starts with 07 and is 10 digits long
	phoneRX := regexp.MustCompile(`^07\d{8}$`)
	v.Check(validator.Matches(user.PhoneNumber, phoneRX), "phone_number", "must be a valid phone number")

	v.Check(user.Gender != "", "gender", "must be provided")
	v.Check(validator.PermittedValue(user.Gender, "male", "female", "other"), "gender", "must be either male, female, or other")

	v.Check(user.County != "", "county", "must be provided")

	// If the password hash is ever nil, this will be due to logical error in the codebase
	if user.Password.Hash == nil {
		panic("missing password hash for user")
	}
}

// Validates raw json from client before mapping to the actual struct
func ValidateUserInput(v *validator.Validator, input *UserInput) {
	v.Check(input.FirstName != nil && *input.FirstName != "", "first_name", "must be provided")
	v.Check(input.LastName != nil && *input.LastName != "", "last_name", "must be provided")
	v.Check(input.Email != nil && *input.Email != "", "email", "must be provided")

	if input.Email != nil {
		ValidateEmail(v, *input.Email)
	}

	v.Check(input.PhoneNumber != nil && *input.PhoneNumber != "", "phone_number", "must be provided")
	v.Check(input.NationalID != nil && *input.NationalID != "", "national_id", "must be provided")
	v.Check(input.Gender != nil && *input.Gender != "", "gender", "must be provided")

	if input.Gender != nil {
		v.Check(validator.PermittedValue(*input.Gender, "male", "female", "other"), "gender", "must be male, female, or other")
	}

	v.Check(input.County != nil && *input.County != "", "county", "must be provided")
	v.Check(input.Password != nil && *input.Password != "", "password", "must be provided")

	if input.Password != nil {
		ValidatePasswordPlaintext(v, *input.Password)
	}
}

// ToUser maps a UserInput DTO to a fully initialized User model
func (input *UserInput) ToUser() (*User, error) {
	// Generate UUID
	userID := uuid.New().String()

	// Hash password
	var password Password
	if input.Password != nil {
		_, err := password.Set(*input.Password)
		if err != nil {
			return nil, fmt.Errorf("error hashing password: %w", err)
		}
	}

	//Build the user model
	user := &User{
		ID:          userID,
		FirstName:   deref(input.FirstName),
		LastName:    deref(input.LastName),
		Email:       deref(input.Email),
		PhoneNumber: deref(input.PhoneNumber),
		NationalID:  deref(input.NationalID),
		Gender:      deref(input.Gender),
		County:      deref(input.County),
		Password:    password,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return user, nil
}

func MapInputToProfile(input UserProfile) *UserProfile {
	return &UserProfile{
		ID:                 uuid.New().String(),
		FirstName:          input.FirstName,
		LastName:           input.LastName,
		NationalID:         input.NationalID,
		Email:              input.Email,
		Phone:              input.Phone,
		Gender:             input.Gender,
		County:             input.County,
		ProfilePhotoURL:    input.ProfilePhotoURL,
		Nationality:        input.Nationality,
		DOB:                input.DOB,
		EducationLevel:     input.EducationLevel,
		WorkExperience:     input.WorkExperience,
		Skills:             input.Skills,
		ResumeURL:          input.ResumeURL,
		CoverLetterURL:     input.CoverLetterURL,
		CertificationsURLs: input.CertificationsURLs,
		DisabilityStatus:   input.DisabilityStatus,
		Availability:       input.Availability,
		PreferredJobType:   input.PreferredJobType,
		PreferredLocations: input.PreferredLocations,
		LinkedInURL:        input.LinkedInURL,
		CreatedAt:          time.Now().UTC(),
		UpdatedAt:          time.Now().UTC(),
	}
}

// Helper function to safely dereference string pointers
func deref(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// ValidateUserProfileInput performs input validation on the user profile fields.
// It checks for required fields, valid formats (like email and date of birth),
// constraints (such as uniqueness of skills), and logical correctness (e.g.,
// date of birth must be in the past).
//
// Parameters:
//   - v: A pointer to a custom Validator instance that accumulates field-level errors.
//   - input: A pointer to the UserProfile struct containing input data from the frontend.
//
// Returns:
//   - An error if validation fails (i.e., if any field does not meet the criteria).
//   - nil if all validations pass.

func ValidateUserProfileInput(v *validator.Validator, input *UserProfile) error {
	v.Check(input.FirstName != "", "first_name", "First name is required")
	v.Check(input.LastName != "", "last_name", "Last name is required")
	v.Check(input.NationalID != "", "national_id", "National ID is required")

	v.Check(input.Email != "", "email", "Email is required")
	v.Check(validator.Matches(input.Email, validator.EmailRX), "email", "Invalid email address")

	v.Check(input.Phone != "", "phone", "Phone number is required")
	v.Check(validator.Matches(input.Phone, regexp.MustCompile(`^(?:\+254|0)?7\d{8}$`)), "phone", "Invalid Kenyan phone number")

	v.Check(validator.PermittedValue(input.Gender, "Male", "Female", "Other", ""), "gender", "Invalid gender value")

	if input.DOB != "" {
		dobTime, err := time.Parse("2006-01-02", input.DOB)
		if err != nil {
			v.AddError("dob", "Invalid date format. Use YYYY-MM-DD")
		} else {
			v.Check(dobTime.Before(time.Now()), "dob", "Date of birth cannot be in the future")
		}
	}

	v.Check(len(input.Skills) <= 10, "skills", "Too many skills (max 10)")
	v.Check(validator.Unique(input.Skills), "skills", "Skills must be unique")

	if input.ResumeURL != "" {
		v.Check(isValidURL(input.ResumeURL), "resume_url", "Invalid resume URL")
	}

	if input.CoverLetterURL != "" {
		v.Check(isValidURL(input.CoverLetterURL), "cover_letter_url", "Invalid cover letter URL")
	}

	if input.LinkedInURL != "" {
		v.Check(isValidURL(input.LinkedInURL), "linkedin_url", "Invalid LinkedIn URL")
	}

	if !v.Valid() {
		return &validator.ValidationError{Errors: v.Errors}
	}
	return nil
}

func isValidURL(u string) bool {
	parsed, err := url.ParseRequestURI(u)
	return err == nil && parsed.Scheme != "" && parsed.Host != ""
}
