package repository

// import (
// 	"context"
// 	"database/sql"
// 	"fmt"
// 	"os"
// 	"testing"
// 	"time"
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

// 	"github.com/joho/godotenv"
// 	_ "github.com/lib/pq"

// 	"county-project/internal/models"
// 	// "county-project/internal/repository"
// )

// func TestUpdateJobUser(t *testing.T) {
// 	// Load .env
// 	err := godotenv.Load("../../.env") // Adjust the path if needed
// 	if err != nil {
// 		t.Fatalf("Error loading .env file: %v", err)
// 	}

// 	dbURL := os.Getenv("DATABASE_URL")
// 	if dbURL == "" {
// 		t.Fatal("DATABASE_URL is not set")
// 	}

// 	db, err := sql.Open("postgres", dbURL)
// 	if err != nil {
// 		t.Fatalf("Failed to connect to DB: %v", err)
// 	}

// 	defer db.Close()

// 	// Your test logic here, e.g.:
// 	repo := NewJobUserRepository(db)
// 	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
// 	_, _ = db.Exec("DELETE FROM job_users WHERE email = $1", email)

// 	user := &models.User{
// 		ID:          1,
// 		FirstName:   "Test",
// 		LastName:    "User",
// 		Email:       email,
// 		PhoneNumber: "0712345678",
// 		NationalID:  fmt.Sprintf("ID%d", time.Now().UnixNano()%1e9),
// 		Gender:      "female",
// 		County:      "Kisumu",
// 	}

// 	// INSERT the user first
// 	insertQuery := `
//     INSERT INTO job_users (first_name, last_name, email, phone_number, national_id, gender, county)
//     VALUES ($1, $2, $3, $4, $5, $6, $7)
//     RETURNING id
// `

// 	err = db.QueryRow(insertQuery,
// 		user.FirstName,
// 		user.LastName,
// 		user.Email,
// 		user.PhoneNumber,
// 		user.NationalID,
// 		user.Gender,
// 		user.County,
// 	).Scan(&user.ID)

// 	if err != nil {
// 		t.Fatalf("Failed to insert user: %v", err)
// 	}

// 	// Now run the update
// 	user.FirstName = "UpdatedName"

// 	err = repo.UpdateJobUser(context.Background(), user)
// 	if err != nil {
// 		t.Errorf("Failed to update job user: %v", err)
// 	}

// }
// func TestCreateJobUser(t *testing.T) {
// 	err := godotenv.Load("../../.env")
// 	if err != nil {
// 		t.Fatalf("Error loading .env: %v", err)
// 	}

// 	dbURL := os.Getenv("DATABASE_URL")
// 	db, err := sql.Open("postgres", dbURL)
// 	if err != nil {
// 		t.Fatalf("Failed to connect: %v", err)
// 	}
// 	defer db.Close()

// 	repo := NewJobUserRepository(db)

// 	user := &models.User{
// 		FirstName:   "Alice",
// 		LastName:    "Tester",
// 		Email:       fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
// 		PhoneNumber: "0700000000",
// 		NationalID:  fmt.Sprintf("ID%d", time.Now().UnixNano()%1e9),
// 		Gender:      "female",
// 		County:      "Nairobi",
// 	}

// 	err = repo.CreateJobUser(context.Background(), user)
// 	if err != nil {
// 		t.Fatalf("Failed to create job user: %v", err)
// 	}

// 	if user.ID == 0 {
// 		t.Error("Expected user to have a valid ID after insert")
// 	}
// }
// func TestGetJobUserByID(t *testing.T) {
// 	err := godotenv.Load("../../.env")
// 	if err != nil {
// 		t.Fatalf("Error loading .env: %v", err)
// 	}

// 	dbURL := os.Getenv("DATABASE_URL")
// 	db, err := sql.Open("postgres", dbURL)
// 	if err != nil {
// 		t.Fatalf("Failed to connect: %v", err)
// 	}
// 	defer db.Close()

// 	repo := NewJobUserRepository(db)

// 	// Create a dummy user
// 	user := &models.User{
// 		FirstName:   "Jane",
// 		LastName:    "Doe",
// 		Email:       fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
// 		PhoneNumber: "0711111111",
// 		NationalID:  fmt.Sprintf("ID%d", time.Now().UnixNano()%1e9),
// 		Gender:      "female",
// 		County:      "Mombasa",
// 	}

// 	err = repo.CreateJobUser(context.Background(), user)
// 	if err != nil {
// 		t.Fatalf("Failed to create user: %v", err)
// 	}

// 	// Fetch the user by ID
// 	fetchedUser, err := repo.GetJobUserByID(context.Background(), user.ID)
// 	if err != nil {
// 		t.Fatalf("Failed to fetch user by ID: %v", err)
// 	}

// 	if fetchedUser.Email != user.Email {
// 		t.Errorf("Expected email %s but got %s", user.Email, fetchedUser.Email)
// 	}
// }
// func TestDeleteJobUser(t *testing.T) {
// 	err := godotenv.Load("../../.env")
// 	if err != nil {
// 		t.Fatalf("Error loading .env: %v", err)
// 	}

// 	dbURL := os.Getenv("DATABASE_URL")
// 	db, err := sql.Open("postgres", dbURL)
// 	if err != nil {
// 		t.Fatalf("Failed to connect: %v", err)
// 	}
// 	defer db.Close()

// 	repo := NewJobUserRepository(db)

// 	// Create user to delete
// 	user := &models.User{
// 		FirstName:   "Mark",
// 		LastName:    "Delete",
// 		Email:       fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
// 		PhoneNumber: "0722222222",
// 		NationalID:  fmt.Sprintf("ID%d", time.Now().UnixNano()%1e9),
// 		Gender:      "male",
// 		County:      "Kisii",
// 	}

// 	err = repo.CreateJobUser(context.Background(), user)
// 	if err != nil {
// 		t.Fatalf("Failed to create user: %v", err)
// 	}

// 	// Now delete
// 	err = repo.DeleteJobUser(context.Background(), user.ID)
// 	if err != nil {
// 		t.Fatalf("Failed to delete user: %v", err)
// 	}

// 	// Verify deletion
// 	_, err = repo.GetJobUserByID(context.Background(), user.ID)
// 	if err == nil {
// 		t.Fatal("Expected error when fetching deleted user, but got none")
// 	}
// }
