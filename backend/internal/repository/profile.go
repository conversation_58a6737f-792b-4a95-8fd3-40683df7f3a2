package repository

import (
	"context"
	"database/sql"

	"github.com/lib/pq"

	"county-project/internal/models"
)

type UserProfileRepository interface {
	InsertProfile(ctx context.Context, profile *models.UserProfile) error
	GetProfileByUserID(ctx context.Context, userID string) (*models.UserProfile, error)
	UpdateProfile(ctx context.Context, profile *models.UserProfile) error
}

// userProfileRepository handles DB operations for user profiles
type userProfileRepository struct {
	db *sql.DB
}

func NewUserProfileRepository(db *sql.DB) UserProfileRepository {
	return &userProfileRepository{db: db}
}

// InsertProfile inserts a new user profile into the database.
// Parameters:
// - ctx: Context for timeout/cancellation propagation.
// - p: Pointer to a UserProfile struct containing profile data.
// Returns:
// - error: Any error that occurred during insertion, or nil on success.
func (r *userProfileRepository) InsertProfile(ctx context.Context, p *models.UserProfile) error {
	query := `
		INSERT INTO user_profiles (
			id, first_name, last_name, national_id, email, phone, gender, county,
			profile_photo_url, nationality, dob, education_level, work_experience,
			skills, resume_url, cover_letter_url, certifications_url, disability_status,
			availability, preferred_job_type, preferred_locations, linkedin_url
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8,
			$9, $10, $11, $12, $13,
			$14, $15, $16, $17, $18,
			$19, $20, $21, $22
		)`

	_, err := r.db.ExecContext(ctx, query,
		p.ID, p.FirstName, p.LastName, p.NationalID, p.Email, p.Phone, p.Gender, p.County,
		p.ProfilePhotoURL, p.Nationality, p.DOB, p.EducationLevel, p.WorkExperience,
		pq.Array(p.Skills), p.ResumeURL, p.CoverLetterURL, pq.Array(p.CertificationsURLs), p.DisabilityStatus,
		p.Availability, p.PreferredJobType, pq.Array(p.PreferredLocations), p.LinkedInURL,
	)
	return err
}

// GetProfileByUserID retrieves a user profile using the associated user ID.
// Parameters:
// - ctx: Context for timeout/cancellation propagation.
// - userID: Unique identifier for the user profile.
// Returns:
// - *models.UserProfile: The populated profile struct (or nil if not found).
// - error: Any error that occurred while querying the DB.
func (r *userProfileRepository) GetProfileByUserID(ctx context.Context, userID string) (*models.UserProfile, error) {
	query := `SELECT id, first_name, last_name, national_id, email, phone, gender, county,
		profile_photo_url, nationality, dob, education_level, work_experience,
		skills, resume_url, cover_letter_url, certifications_url, disability_status,
		availability, preferred_job_type, preferred_locations, linkedin_url,
		created_at, updated_at
		FROM user_profiles WHERE id = $1`

	row := r.db.QueryRowContext(ctx, query, userID)

	var p models.UserProfile
	err := row.Scan(
		&p.ID, &p.FirstName, &p.LastName, &p.NationalID, &p.Email, &p.Phone, &p.Gender, &p.County,
		&p.ProfilePhotoURL, &p.Nationality, &p.DOB, &p.EducationLevel, &p.WorkExperience,
		pq.Array(&p.Skills), &p.ResumeURL, &p.CoverLetterURL, pq.Array(&p.CertificationsURLs), &p.DisabilityStatus,
		&p.Availability, &p.PreferredJobType, pq.Array(&p.PreferredLocations), &p.LinkedInURL,
		&p.CreatedAt, &p.UpdatedAt,
	)
	if err == sql.ErrNoRows {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &p, nil
}

// UpdateProfile updates the values of an existing user profile in the database.
// Parameters:
// - ctx: Context for timeout/cancellation propagation.
// - p: Pointer to the UserProfile struct containing updated data.
// Returns:
// - error: Any error that occurred during update, or nil on success.
func (r *userProfileRepository) UpdateProfile(ctx context.Context, p *models.UserProfile) error {
	query := `
		UPDATE user_profiles SET
			first_name = $2,
			last_name = $3,
			national_id = $4,
			email = $5,
			phone = $6,
			gender = $7,
			county = $8,
			profile_photo_url = $9,
			nationality = $10,
			dob = $11,
			education_level = $12,
			work_experience = $13,
			skills = $14,
			resume_url = $15,
			cover_letter_url = $16,
			certifications_url = $17,
			disability_status = $18,
			availability = $19,
			preferred_job_type = $20,
			preferred_locations = $21,
			linkedin_url = $22,
			updated_at = timezone('utc', now())
		WHERE id = $1`

	_, err := r.db.ExecContext(ctx, query,
		p.ID, p.FirstName, p.LastName, p.NationalID, p.Email, p.Phone, p.Gender, p.County,
		p.ProfilePhotoURL, p.Nationality, p.DOB, p.EducationLevel, p.WorkExperience,
		pq.Array(p.Skills), p.ResumeURL, p.CoverLetterURL, pq.Array(p.CertificationsURLs), p.DisabilityStatus,
		p.Availability, p.PreferredJobType, pq.Array(p.PreferredLocations), p.LinkedInURL,
	)
	return err
}
