package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"county-project/internal/models"
)

type TokenRepository interface {
	Insert(ctx context.Context, token *models.Token) error
	DeleteAllForUser(token *models.Token) error
	DeleteByHash(ctx context.Context, tokenHash []byte, scope string) error
	GetForToken(ctx context.Context, tokenScope string, tokenHash []byte) (*models.User, error)
}

// satisfies the interface
type tokenRepository struct {
	db *sql.DB
}

// creates a new instance of the TokenRepository
func NewTokenRepository(db *sql.DB) TokenRepository {
	return &tokenRepository{db: db}
}

// Insert adds a new token record to the tokens table.
func (r *tokenRepository) Insert(ctx context.Context, token *models.Token) error {
	query := `
        INSERT INTO tokens (hash, user_id, expiry, scope)
        VALUES ($1, $2, $3, $4)`

	args := []any{token.Hash, token.UserID, token.Expiry, token.Scope}

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	_, err := r.db.ExecContext(ctx, query, args...)
	return err
}

// DeleteAllForUser() deletes all tokens for a specific user and scope.
func (r *tokenRepository) DeleteAllForUser(token *models.Token) error {
	query := `
DELETE FROM tokens
WHERE scope = $1 AND user_id = $2`
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	_, err := r.db.ExecContext(ctx, query, token.Scope, token.UserID)
	return err
}

// DeleteByHash deletes a single token from the database based on its SHA-256 hash and scope.
// This is used for securely logging out the current session/token only.
// It's especially useful when the user sends the raw token via the Authorization header,
// and we don't have access to the user ID yet.
// Note: This is different from DeleteAllForUser(), which deletes all tokens
// for a given user ID and scope (i.e., logging the user out from all devices).
// Use this when you want to invalidate only the current token during logout,
// without affecting other active sessions.

func (r *tokenRepository) DeleteByHash(ctx context.Context, tokenHash []byte, scope string) error {
	query := `
		DELETE FROM tokens
		WHERE hash = $1 AND scope = $2`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	result, err := r.db.ExecContext(ctx, query, tokenHash, scope)
	if err != nil {
		return err
	}

	//gives us the number of affected rows
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	// we first check if any rows were actually deleted. If not, the token didn't exist in first place
	if rowsAffected == 0 {
		return ErrRecordNotFound
	}
	return err
}

func (r *tokenRepository) GetForToken(ctx context.Context, tokenScope string, tokenHash []byte) (*models.User, error) {
	// setup the query to lookup userID from the table based on the tokenHash
	query := `
        SELECT
            job_users.id, job_users.first_name, job_users.last_name, job_users.email,
            job_users.phone_number, job_users.national_id, job_users.gender, job_users.county,
            job_users.created_at, job_users.updatedat, job_users.password_hash
        FROM
            job_users
        INNER JOIN tokens
            ON job_users.id = tokens.user_id
        WHERE
            tokens.hash = $1
            AND tokens.scope = $2
            AND tokens.expiry > $3`

	// Create a new context with a 3-second timeout.
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	var user models.User

	//Use QueryRowContext to execute the query
	err := r.db.QueryRowContext(ctx, query, tokenHash, tokenScope, time.Now()).Scan(
		&user.ID,
		&user.FirstName,
		&user.LastName,
		&user.Email,
		&user.PhoneNumber,
		&user.NationalID,
		&user.Gender,
		&user.County,
		&user.CreatedAt,
		&user.UpdatedAt,
		&user.Password.Hash,
	)

	if err != nil {
		switch {
		// If no matching record is found, we return the standard ErrRecordNotFound.
		case errors.Is(err, sql.ErrNoRows):
			return nil, ErrRecordNotFound
		default:
			return nil, err
		}
	}
	return &user, nil

}
