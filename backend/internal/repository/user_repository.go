package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/lib/pq"

	"county-project/internal/models"
)

// internal/models/errors.go or in the same file
var (
	ErrDuplicateEmail      = errors.New("email already exists")
	ErrDuplicateNationalID = errors.New("national ID already exists")
)

var ErrInvalidCredentials = errors.New("invalid credentials")

type JobUserRepository interface {
	CreateJobUser(ctx context.Context, user *models.User) (*models.User, error)
	GetJobUserByID(ctx context.Context, id string) (*models.User, error)
	UpdateJobUser(ctx context.Context, user *models.User) error // ✅ Ensure this line exists
	DeleteJobUser(ctx context.Context, id string) error
	GetJobUserByEmail(ctx context.Context, email string) (*models.User, error)
}

type jobUserRepository struct {
	db *sql.DB
}

func NewJobUserRepository(db *sql.DB) JobUserRepository {
	return &jobUserRepository{db: db}
}

var ErrRecordNotFound = errors.New("record not found")

func (r *jobUserRepository) CreateJobUser(ctx context.Context, user *models.User) (*models.User, error) {
	query := `
	INSERT INTO job_users 
	(id, first_name, last_name, email, phone_number, national_id, gender, county, password_hash, created_at, updated_at)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	RETURNING id, created_at`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	err := r.db.QueryRowContext(ctx, query,
		user.ID,
		user.FirstName,
		user.LastName,
		user.Email,
		user.PhoneNumber,
		user.NationalID,
		user.Gender,
		user.County,
		user.Password.Hash,
		user.CreatedAt,
		user.UpdatedAt,
	).Scan(&user.ID, &user.CreatedAt)

	if err != nil {
		var pgErr *pq.Error
		if errors.As(err, &pgErr) && pgErr.Code == "23505" {
			switch pgErr.Constraint {
			case "job_users_email_key":
				return nil, ErrDuplicateEmail
			case "job_users_national_id_key":
				return nil, ErrDuplicateNationalID
			}
		}
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// Gets a user by their email since during registratio we do not know the id of the
// curent user, hence checking the email provided becomes the most viable
// during signup
func (r *jobUserRepository) GetJobUserByEmail(ctx context.Context, email string) (*models.User, error) {
	query := `
		SELECT id, first_name, last_name, email, phone_number, national_id, gender, county, created_at, updated_at,password_hash
		FROM job_users
		WHERE email = $1`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	var user models.User
	err := r.db.QueryRowContext(ctx, query, email).Scan(
		&user.ID,
		&user.FirstName,
		&user.LastName,
		&user.Email,
		&user.PhoneNumber,
		&user.NationalID,
		&user.Gender,
		&user.County,
		&user.CreatedAt,
		&user.UpdatedAt,
		&user.Password.Hash,
	)

	if err != nil {
		switch {
		case errors.Is(err, sql.ErrNoRows):
			return nil, ErrRecordNotFound
		default:
			return nil, err
		}
	}

	return &user, nil
}

func (r *jobUserRepository) GetJobUserByID(ctx context.Context, id string) (*models.User, error) {
	query := `
		SELECT id, first_name, last_name, email, phone_number, national_id, gender, county, created_at
		FROM job_users
		WHERE id = $1`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	var user models.User
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&user.ID,
		&user.FirstName,
		&user.LastName,
		&user.Email,
		&user.PhoneNumber,
		&user.NationalID,
		&user.Gender,
		&user.County,
		&user.CreatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("user not found: %w", sql.ErrNoRows)
		}
		return nil, fmt.Errorf("failed to get user by id: %w", err)
	}
	return &user, nil
}

func (r *jobUserRepository) UpdateJobUser(ctx context.Context, user *models.User) error {
	query := `
		UPDATE job_users
		SET first_name = $1, last_name = $2, email = $3,
			phone_number = $4, national_id = $5, gender = $6, county = $7
		WHERE id = $8`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	_, err := r.db.ExecContext(ctx, query,
		user.FirstName,
		user.LastName,
		user.Email,
		user.PhoneNumber,
		user.NationalID,
		user.Gender,
		user.County,
		user.ID,
	)

	return err
}

func (r *jobUserRepository) DeleteJobUser(ctx context.Context, id string) error {
	query := `DELETE FROM job_users WHERE id = $1`

	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	_, err := r.db.ExecContext(ctx, query, id)
	return err
}
