package services

import (
	"context"
	"errors"

	"county-project/internal/models"
	"county-project/internal/repository"
	"county-project/internal/validator"
)

type UserProfileService interface {
	CreateProfile(ctx context.Context, input models.UserProfile) error
	GetProfile(ctx context.Context, userID string) (*models.UserProfile, error)
	UpdateProfile(ctx context.Context, input models.UserProfile) error
}

type userProfileService struct {
	repo repository.UserProfileRepository
}

func NewUserProfileService(repo repository.UserProfileRepository) UserProfileService {
	return &userProfileService{repo: repo}
}

// CreateProfile validates and inserts a new profile
func (s *userProfileService) CreateProfile(ctx context.Context, input models.UserProfile) error {
	// Initialize validator
	v := validator.New()

	// Convert input to UserProfile
	profile := models.MapInputToProfile(input)

	// Validate
	if err := models.ValidateUserProfileInput(v, profile); err != nil {
		return err
	}

	return s.repo.InsertProfile(ctx, profile)
}

// GetProfile retrieves a profile by user ID
func (s *userProfileService) GetProfile(ctx context.Context, userID string) (*models.UserProfile, error) {
	if userID == "" {
		return nil, errors.New("user ID is required")
	}
	return s.repo.GetProfileByUserID(ctx, userID)
}

// UpdateProfile validates and updates an existing profile
func (s *userProfileService) UpdateProfile(ctx context.Context, input models.UserProfile) error {
	// Initialize validator
	v := validator.New()

	// Convert input to UserProfile
	profile := models.MapInputToProfile(input)

	// Validate
	if err := models.ValidateUserProfileInput(v, profile); err != nil {
		return err
	}

	// Proceed to update
	return s.repo.UpdateProfile(ctx, profile)
}
