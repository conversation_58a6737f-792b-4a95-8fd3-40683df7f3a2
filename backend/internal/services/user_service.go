package services

import (
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"time"

	"county-project/internal/models"
	"county-project/internal/repository"
	"county-project/internal/validator"
)

type JobUserService interface {
	CreateJobUserService(ctx context.Context, input *models.UserInput) (*models.User, error)
	GetJobUserByIDService(ctx context.Context, id string) (*models.User, error)
	UpdateJobUserService(ctx context.Context, user *models.User) error
	DeleteJobUserService(ctx context.Context, id string) error
	Authenticate(ctx context.Context, input *models.LoginInput) (*models.Token, error)
	Logout(ctx context.Context, tokenPlaintext string) error
	GetUserForToken(ctx context.Context, tokenPlaintext string) (*models.User, error)
}

type jobUserService struct {
	repo      repository.JobUserRepository
	tokenRepo repository.TokenRepository
}

func NewJobUserService(repo repository.JobUserRepository, tokenRepo repository.TokenRepository) JobUserService {
	return &jobUserService{
		repo:      repo,
		tokenRepo: tokenRepo,
	}
}

func (s *jobUserService) CreateJobUserService(ctx context.Context, input *models.UserInput) (*models.User, error) {
	fmt.Println("step 2")

	//create an instance of validator
	v := validator.New()

	//validate the raw inpu json from client
	models.ValidateUserInput(v, input)

	if !v.Valid() {
		//return validation error incase of error
		return nil, &validator.ValidationError{Errors: v.Errors}
	}

	fmt.Println("step 5")
	_, err := s.repo.GetJobUserByEmail(ctx, *input.Email)
	if err != nil {
		// If the error is NOT "record not found", then something else went wrong.
		if !errors.Is(err, repository.ErrRecordNotFound) {
			return nil, err // Return the actual database error
		}
		// If it IS ErrRecordNotFound, that's good The email is unique. We can continue.
	} else {
		// If err is nil, it means a user WAS found. This is a duplicate.
		return nil, repository.ErrDuplicateEmail
	}

	//map data from raw json to the actual struct going into the database
	user, err := input.ToUser()
	if err != nil {
		return nil, err
	}

	fmt.Println("step 4")
	//call the repo layer that knows how to talk to db to insert user into the database
	_, err = s.repo.CreateJobUser(ctx, user)
	if err != nil {
		return nil, err
	}
	fmt.Println("step3")
	return user, nil
}

// Implement the new Authenticate method
func (s *jobUserService) Authenticate(ctx context.Context, input *models.LoginInput) (*models.Token, error) {
	//Validate the login credentials, email and password
	v := validator.New()
	models.ValidateEmail(v, *input.Email)
	models.ValidatePasswordPlaintext(v, *input.Password)

	if !v.Valid() {
		//return validation error incase of error
		return nil, &validator.ValidationError{Errors: v.Errors}
	}

	// Lookup the user record based on the email address. If no matching user was found
	//return errRecordNotFound
	user, err := s.repo.GetJobUserByEmail(ctx, *input.Email)
	if err != nil {
		switch {
		case errors.Is(err, repository.ErrRecordNotFound):
			return nil, repository.ErrInvalidCredentials
		default:
			return nil, err
		}
	}

	// Check if the provided password matches the actual password for the user.
	match, err := user.Password.Matches(*input.Password)
	if err != nil {
		//server error
		return nil, err
	}
	if !match {
		return nil, repository.ErrInvalidCredentials
	}

	// Otherwise, if the password is correct, we generate a new token with a 24-hour
	// expiry time and the scope 'authentication'.
	token, err := models.GenerateToken(user.ID, 24*time.Hour, models.ScopeAuthentication)
	if err != nil {
		return nil, err
	}

	// Save the token to the database.
	err = s.tokenRepo.Insert(ctx, token)
	if err != nil {
		return nil, err
	}

	//return token containing the plain text to the handler
	return token, nil

}

// Logout invalidates a user's authentication token by deleting it from the database.
// This effectively logs the user out on the backend and prevents further use of the token.
func (s *jobUserService) Logout(ctx context.Context, tokenPlaintext string) error {
	// Ensure the token is the expected length (26 chars for your system)
	//we call our custom validate tokenPlaintext
	v := validator.New()
	models.ValidateTokenPlaintext(v, tokenPlaintext)
	if !v.Valid() {
		fmt.Println("validation error:")
		return &validator.ValidationError{Errors: v.Errors}
	}
	// Hash the plaintext token using SHA-256 to match the stored value in the DB
	hash := sha256.Sum256([]byte(tokenPlaintext))
	tokenHash := hash[:]

	// Delete the token from the database using the hash and authentication scope
	// This removes the session and logs the user out
	//we now look for the errrecordnot found and return nil, user was not in db
	err := s.tokenRepo.DeleteByHash(ctx, tokenHash, models.ScopeAuthentication)
	if err != nil {
		switch {
		case errors.Is(err, repository.ErrRecordNotFound):
			return nil
		default:
			return err

		}
	}
	return nil
}

// retrieves a specific user associated with the hashed token
func (s *jobUserService) GetUserForToken(ctx context.Context, tokenPlaintext string) (*models.User, error) {
	// Validate the token plaintext.
	v := validator.New()
	models.ValidateTokenPlaintext(v, tokenPlaintext)
	if !v.Valid() {
		return nil, &validator.ValidationError{Errors: v.Errors}
	}

	//Hash the plaintext token.
	hash := sha256.Sum256([]byte(tokenPlaintext))

	//Call the repository to get the associated user using the correct scope for the token
	user, err := s.tokenRepo.GetForToken(ctx, models.ScopeAuthentication, hash[:])
	if err != nil {
		switch {
		// If the token wasn't found in the database, the credentials are bad.
		case errors.Is(err, repository.ErrRecordNotFound):
			return nil, repository.ErrInvalidCredentials
		default:
			return nil, err
		}
	}

	//Return the user.
	return user, nil
}

// GetJobUserByIDService retrieves a user from the database by their unique ID.
// This is important for fetching user details for profile views, admin actions, or internal logic.
func (s *jobUserService) GetJobUserByIDService(ctx context.Context, id string) (*models.User, error) {
	return s.repo.GetJobUserByID(ctx, id)
}

// UpdateJobUserService updates an existing user's information in the database.
// This is important for allowing users or admins to change user details, such as profile updates.
func (s *jobUserService) UpdateJobUserService(ctx context.Context, user *models.User) error {
	return s.repo.UpdateJobUser(ctx, user)
}

// DeleteJobUserService removes a user from the database by their ID.
// This is important for account deletion, user management, and maintaining data integrity.
func (s *jobUserService) DeleteJobUserService(ctx context.Context, id string) error {
	return s.repo.DeleteJobUser(ctx, id)
}
