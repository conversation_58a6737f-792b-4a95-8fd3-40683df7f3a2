create table if not exists public.user_profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  first_name text not null,
  last_name text not null,
  national_id text not null unique,
  email text not null,
  phone text not null unique,
  gender text,
  county text,
  profile_photo_url text,
  nationality text,
  dob date,
  education_level text,
  work_experience text,
  skills text[],
  resume_url text,
  cover_letter_url text,
  certifications_url text[],
  disability_status text,
  availability text,
  preferred_job_type text,
  preferred_locations text[],
  linkedin_url text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Optional: Enable Row Level Security and Policy
alter table public.user_profiles enable row level security;

create policy "Users can manage their own profile"
on public.user_profiles
for all
using (auth.uid() = id)
with check (auth.uid() = id);




