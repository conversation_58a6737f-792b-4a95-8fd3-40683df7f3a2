# User Journey Overview

This document summarizes the core user journey for our online job portal project. It highlights key stages a typical user goes through — from discovering the platform to applying for jobs.It helps the team understand key interaction points, emotions, and potential pain areas to improve the overall experience.

## Purpose

To understand the user's motivations, pain points, and key interactions across the platform. This helps guide design, development, and testing decisions.

## Key Stages

1. **Sign-Up & Onboarding** – Quick and smooth account creation
2. **Profile Setup** – Uploading resumes and filling experience
3. **Job Search** – Browsing/filtering job listings
4. **Application** – Applying for jobs with minimal friction
5. **Follow-Up** – Tracking status and notifications post-application

##  Full Journey
The full detailed journey can be found here: [View Full User Journey](<https://www.figma.com/design/m6hkUYJXsAXT6d7SuWufKN/county?node-id=0-1&p=f&t=j8WMLwSfIha5IEe0-0>)



