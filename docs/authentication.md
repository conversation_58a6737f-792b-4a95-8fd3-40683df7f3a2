# Authentication System Documentation

## Overview

The Kisumu County Job Portal implements a global authentication state management system using React Context API. This system provides centralized user authentication, session management, and secure token handling throughout the application.

## Architecture

### Core Components

1. **AuthContext** (`frontend/src/contexts/AuthContext.jsx`)
   - Main authentication context provider
   - Manages global authentication state
   - Handles localStorage persistence

2. **Authentication Utilities** (`frontend/src/utils/authUtils.js`)
   - Helper functions for authentication operations
   - Token validation and management
   - API request utilities

3. **App Integration** (`frontend/src/App.jsx`)
   - AuthProvider wrapper for global access
   - Framework-agnostic implementation

## Features

### Authentication State Management
- User data storage and retrieval
- JWT token management
- Session persistence across browser refreshes
- Loading states for authentication checks

### Core Functions
- `login(userData, token)` - Authenticate user and store session
- `logout()` - Clear authentication state and session data
- `updateUser(userData)` - Update user information without re-authentication

### Security Features
- Token expiration validation
- Corrupted localStorage data handling
- Automatic session cleanup on errors
- Secure token storage patterns

## Usage

### Basic Implementation

```jsx
import { useAuth } from './contexts/AuthContext';

function MyComponent() {
  const { 
    user, 
    token, 
    isAuthenticated, 
    isLoading, 
    login, 
    logout 
  } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Login Implementation

```jsx
import { useAuth } from './contexts/AuthContext';

function LoginForm() {
  const { login } = useAuth();

  const handleLogin = async (credentials) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      });

      const { user, token } = await response.json();
      login(user, token);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  // ... rest of component
}
```

### Protected Routes

```jsx
import { useAuth } from './contexts/AuthContext';

function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Access denied</div>;

  return children;
}
```

### Authenticated API Calls

```jsx
import { useAuth } from './contexts/AuthContext';
import { authenticatedFetch } from './utils/authUtils';

function useApiCall() {
  const { token } = useAuth();

  const makeRequest = async (url, options = {}) => {
    return authenticatedFetch(url, options, token);
  };

  return makeRequest;
}
```

## API Reference

### useAuth Hook

Returns an object with the following properties:

| Property | Type | Description |
|----------|------|-------------|
| `user` | Object\|null | Current user data |
| `token` | string\|null | Authentication token |
| `isAuthenticated` | boolean | Authentication status |
| `isLoading` | boolean | Loading state indicator |
| `login(userData, token)` | function | Authenticate user |
| `logout()` | function | Clear authentication |
| `updateUser(userData)` | function | Update user data |

### Utility Functions

| Function | Description |
|----------|-------------|
| `isTokenExpired(token)` | Check if JWT token is expired |
| `createAuthHeader(token)` | Create Authorization header |
| `validateUserData(userData)` | Validate user data structure |
| `hasRole(user, role)` | Check user permissions |
| `authenticatedFetch(url, options, token)` | Make authenticated requests |

## Storage

### localStorage Keys
- `authToken` - JWT authentication token
- `authUser` - User data (JSON string)

### Data Structure

```javascript
// User object structure
{
  id: "string",
  email: "string",
  name: "string",
  role: "string",
  // ... other user properties
}
```

## Security Considerations

### Current Implementation
- Tokens stored in localStorage for persistence
- Client-side token expiration checking
- Automatic cleanup of corrupted data
- Input validation for authentication functions

### Recommendations for Production
1. **Token Storage**: Consider httpOnly cookies for enhanced security
2. **Token Refresh**: Implement automatic token refresh mechanism
3. **HTTPS**: Ensure all authentication endpoints use HTTPS
4. **Server Validation**: Always validate tokens server-side
5. **Rate Limiting**: Implement login attempt rate limiting

## Error Handling

The authentication system handles various error scenarios:

- **Corrupted localStorage**: Automatically clears invalid data
- **Network Errors**: Graceful degradation with error messages
- **Token Expiration**: Automatic logout on expired tokens
- **Invalid Credentials**: Proper error propagation to UI

## Testing

### Unit Tests (Recommended)
```javascript
// Example test structure
describe('AuthContext', () => {
  test('should login user successfully', () => {
    // Test login functionality
  });

  test('should logout user and clear storage', () => {
    // Test logout functionality
  });

  test('should handle corrupted localStorage', () => {
    // Test error handling
  });
});
```

## Integration Guide

### Step 1: Wrap Your App
```jsx
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      {/* Your app components */}
    </AuthProvider>
  );
}
```

### Step 2: Use in Components
```jsx
import { useAuth } from './contexts/AuthContext';

function MyComponent() {
  const { isAuthenticated } = useAuth();
  // Use authentication state
}
```

### Step 3: Implement Login/Logout
```jsx
const { login, logout } = useAuth();

// After successful API call
login(userData, token);

// On logout button click
logout();
```

## Troubleshooting

### Common Issues

1. **"useAuth must be used within an AuthProvider"**
   - Ensure component is wrapped with AuthProvider
   - Check component hierarchy

2. **Authentication state not persisting**
   - Verify localStorage is available
   - Check for localStorage quota limits

3. **Token expiration issues**
   - Implement token refresh mechanism
   - Handle 401 responses from API

### Debug Tips
- Check browser localStorage for auth data
- Monitor network requests for token headers
- Use React DevTools to inspect context state

## Future Enhancements

1. **Token Refresh**: Automatic token renewal
2. **Role-Based Access**: Enhanced permission system
3. **Multi-Factor Authentication**: Additional security layer
4. **Session Management**: Advanced session handling
5. **Audit Logging**: Track authentication events
