import { BrowserRouter, Routes, Route } from 'react-router-dom'

// Import page components
import Home from './pages/Home'
import Login from './pages/Login'
import Register from './pages/Register'

// Import authentication context
import { AuthProvider } from './contexts/AuthContext'

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <div className="app">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            {/* Add more routes as needed */}
          </Routes>
        </div>
      </AuthProvider>
    </BrowserRouter>
  )
}

export default App
