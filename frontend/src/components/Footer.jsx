import { Link } from 'react-router-dom';
import '../styles/components/footer.css';

const Footer = () => {
  const jobLinks = [
    { name: "Latest Listings", href: "/jobs" },
    { name: "Remote Jobs", href: "/jobs?type=remote" },
    { name: "Government Jobs", href: "/jobs?category=government" },
    { name: "Tech Jobs", href: "/jobs?category=tech" },
    { name: "View All", href: "/jobs" }
  ];

  const resourceLinks = [
    { name: "Blog", href: "/blog" },
    { name: "Career Tips", href: "/career-tips" },
    { name: "Resume Builder", href: "/resume-builder" },
    { name: "FAQs", href: "/faqs" },
    { name: "Contact Us", href: "/contact" }
  ];

  const companyLinks = [
    { name: "About", href: "/about" },
    { name: "Our Team", href: "/team" },
    { name: "Terms of Use", href: "/terms" },
    { name: "Privacy Policy", href: "/privacy" }
  ];

  const socialLinks = [
    { name: "X", href: "#", icon: "X" },
    { name: "Instagram", href: "#", icon: "📷" },
    { name: "LinkedIn", href: "#", icon: "in" },
    { name: "YouTube", href: "#", icon: "▶" }
  ];

  return (
    <footer className="footer">
      {/* Main Footer Content */}
      <div className="footer-container">
        {/* Header Section */}
        <div className="footer-header">
          <h2 className="footer-cta-title">Discover Other Job Opportunities</h2>
          <Link to="/jobs" className="btn btn-footer-cta">
            Explore All Jobs
          </Link>
        </div>

        {/* Footer Content Grid */}
        <div className="footer-grid">
          {/* Company Info */}
          <div className="footer-company">
            <div className="footer-logo">JOBLINK</div>
            <p className="footer-description">
              Get job alerts and career tips straight to your inbox. We only send you relevant opportunities.
            </p>

            {/* Social Media Icons */}
            <div className="social-links">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="social-link"
                  aria-label={social.name}
                >
                  <span>{social.icon}</span>
                </a>
              ))}
            </div>
          </div>

          {/* Jobs Column */}
          <div className="footer-column">
            <h3 className="footer-column-title">Jobs</h3>
            <ul className="footer-links">
              {jobLinks.map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="footer-link">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources Column */}
          <div className="footer-column">
            <h3 className="footer-column-title">Resources</h3>
            <ul className="footer-links">
              {resourceLinks.map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="footer-link">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Column */}
          <div className="footer-column">
            <h3 className="footer-column-title">Company</h3>
            <ul className="footer-links">
              {companyLinks.map((link, index) => (
                <li key={index}>
                  <Link to={link.href} className="footer-link">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="footer-bottom">
        <div className="footer-bottom-container">
          <div className="footer-bottom-content">
            <p className="footer-copyright">© 2025 JOBLINK. All rights reserved.</p>
            <div className="footer-bottom-links">
              <Link to="/privacy" className="footer-bottom-link">
                Privacy Policy
              </Link>
              <Link to="/terms" className="footer-bottom-link">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
