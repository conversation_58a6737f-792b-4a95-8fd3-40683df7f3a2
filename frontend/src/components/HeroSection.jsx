import { useState } from 'react';
import { Link } from 'react-router-dom';
import '../styles/components/hero-section.css';

const HeroSection = () => {
  const [jobTitle, setJobTitle] = useState('');
  const [location, setLocation] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    // Handle search logic here
    console.log('Searching for:', { jobTitle, location });
  };

  return (
    <main className="hero-container">
      <div className="hero-grid">
        {/* Left Content */}
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              We Help You Find
              <br />
              Your <span className="hero-highlight">Dream Job.</span>
            </h1>
            <p className="hero-description">
              Join our platform to be paired with one of our top skilled talent with global tech.
            </p>
          </div>

          {/* Apply Now Button */}
          <Link to="/register" className="btn btn-apply">
            <span>Apply Now</span>
            <span className="arrow-icon">→</span>
          </Link>

          {/* Search Form */}
          <div className="search-form">
            <form onSubmit={handleSearch} className="search-grid">
              {/* Job Title Input */}
              <div className="search-field">
                <div className="search-icon">🔍</div>
                <input
                  type="text"
                  placeholder="Job title keyword"
                  value={jobTitle}
                  onChange={(e) => setJobTitle(e.target.value)}
                  className="search-input"
                />
              </div>

              {/* Location Select */}
              <div className="search-field">
                <div className="search-icon">📍</div>
                <select
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="search-select"
                >
                  <option value="">Select Area</option>
                  <option value="kisumu">Kisumu</option>
                  <option value="nairobi">Nairobi</option>
                  <option value="mombasa">Mombasa</option>
                  <option value="remote">Remote</option>
                </select>
              </div>

              {/* Search Button */}
              <button type="submit" className="btn btn-search">
                Search
              </button>
            </form>
          </div>
        </div>

        {/* Right Content - Professional Image */}
        <div className="hero-image">
          <div className="image-container">
            <div className="image-background">
              <img
                src="/heroimage.png"
                alt="Professional team working together - Kisumu County Job Portal"
                className="hero-main-image"
              />
            </div>
          </div>
          {/* Background decoration */}
          <div className="image-decoration"></div>
        </div>
      </div>
    </main>
  );
};

export default HeroSection;
