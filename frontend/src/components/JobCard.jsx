
import React from 'react';
import { Link } from 'react-router-dom';
import '../styles/components/jobcard.css';

const JobCard = ({ job }) => {
  const getDeadlineStatusClass = (deadline) => {
    const today = new Date();
    // Parse DD/Mon/YYYY format
    const parts = deadline.split('/');
    const day = parseInt(parts[0], 10);
    const month = new Date(Date.parse(parts[1] + ' 1, 2000')).getMonth(); // Parse month name
    const year = parseInt(parts[2], 10);

    const deadlineDate = new Date(year, month, day);

    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 30) {
      return 'deadline-new'; // Blue
    } else if (diffDays > 7) {
      return 'deadline-almost-due'; // Green
    } else if (diffDays >= 0) {
      return 'deadline-very-due'; // Red
    } else {
      return 'deadline-passed'; // Optional: for past deadlines
    }
  };

  const deadlineClass = getDeadlineStatusClass(job.deadline);

  return (
    <div className="job-card">
      <h3>{job.title}</h3>
      <div className="job-meta">
        <p><strong>Location:</strong> {job.location}</p>
        <p><strong>Type:</strong> {job.type}</p>
        <p><strong>Experience:</strong> {job.experience}</p>
        <p><strong>Salary:</strong> {job.salary}</p>
      </div>
      <div className={`job-badge ${deadlineClass}`}>
        Due on : {job.deadline}
      </div>
      <Link to="/register" className="btn btn-apply-now">
        Apply now
      </Link>
    </div>
  );
};

export default JobCard;