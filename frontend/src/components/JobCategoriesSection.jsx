import { useState } from 'react';
import { Link } from 'react-router-dom';
import '../styles/components/job-categories-section.css';

const JobCategoriesSection = () => {
  const [activeTab, setActiveTab] = useState("department");

  const departmentJobs = [
    {
      title: "Engineering & Development",
      jobs: ["Frontend Developer", "Backend Developer", "Full Stack Developer", "DevOps Engineer"]
    },
    {
      title: "Design & Creative",
      jobs: ["UX Designer", "UI Designer", "Graphic Designer", "Product Designer"]
    },
    {
      title: "Marketing & Communications",
      jobs: ["Digital Marketing Manager", "Content Writer", "Social Media Manager", "SEO Specialist"]
    },
    {
      title: "Customer Support & Success",
      jobs: ["Customer Success Manager", "Support Specialist", "Technical Support", "Account Manager"]
    }
  ];

  const locationJobs = [
    { name: "Kisumu, Kenya", count: "1,234 jobs available" },
    { name: "Nairobi, Kenya", count: "987 jobs available" },
    { name: "Mombasa, Kenya", count: "756 jobs available" },
    { name: "Remote", count: "2,145 jobs available" }
  ];

  const popularJobs = [
    { title: "Software Engineer", description: "High demand across all industries", openings: "345 openings", badge: "Hot", badgeColor: "green" },
    { title: "Product Manager", description: "Leading product development teams", openings: "128 openings", badge: "Trending", badgeColor: "orange" },
    { title: "UX Designer", description: "Creating user-centered designs", openings: "89 openings", badge: "Popular", badgeColor: "blue" },
    { title: "Data Scientist", description: "Analyzing data for business insights", openings: "156 openings", badge: "Growing", badgeColor: "purple" }
  ];

  return (
    <section className="job-categories-section">
      <div className="categories-container">
        {/* Tab Navigation */}
        <div className="tab-navigation">
          <button
            onClick={() => setActiveTab("department")}
            className={`tab-button ${activeTab === "department" ? "active" : ""}`}
          >
            Jobs by Department
          </button>
          <button
            onClick={() => setActiveTab("location")}
            className={`tab-button ${activeTab === "location" ? "active" : ""}`}
          >
            <span className="tab-icon">📍</span>
            Jobs by Location
          </button>
          <button
            onClick={() => setActiveTab("popular")}
            className={`tab-button ${activeTab === "popular" ? "active" : ""}`}
          >
            Popular Jobs
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === "department" && (
            <div className="department-grid">
              {departmentJobs.map((department, index) => (
                <div key={index} className="department-card">
                  <h4 className="department-title">{department.title}</h4>
                  <ul className="job-list">
                    {department.jobs.map((job, jobIndex) => (
                      <li key={jobIndex}>
                        <Link to="/jobs" className="job-link">
                          {job}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          {activeTab === "location" && (
            <div className="location-grid">
              {locationJobs.map((location, index) => (
                <div key={index} className="location-card">
                  <div className="location-icon">
                    <span>📍</span>
                  </div>
                  <h4 className="location-name">{location.name}</h4>
                  <p className="location-count">{location.count}</p>
                  <Link to="/jobs" className="btn btn-location">
                    View Jobs
                  </Link>
                </div>
              ))}
            </div>
          )}

          {activeTab === "popular" && (
            <div className="popular-grid">
              {popularJobs.map((job, index) => (
                <div key={index} className="popular-card">
                  <div className="popular-header">
                    <h4 className="popular-title">{job.title}</h4>
                    <span className={`badge badge-${job.badgeColor}`}>{job.badge}</span>
                  </div>
                  <p className="popular-description">{job.description}</p>
                  <div className="popular-footer">
                    <span className="popular-openings">{job.openings}</span>
                    <Link to="/jobs" className="btn btn-popular">
                      Apply
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default JobCategoriesSection;
