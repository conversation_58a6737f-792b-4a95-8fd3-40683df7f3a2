import '../styles/components/statistics-section.css';

const StatisticsSection = () => {
  const statistics = [
    {
      value: "85%",
      label: "Jobs Available"
    },
    {
      value: "1200",
      label: "Applications Processed"
    },
    {
      value: "95%",
      label: "User Satisfaction"
    }
  ];

  return (
    <section className="statistics-section">
      <div className="statistics-container">
        <h2 className="statistics-title">Statistics at a Glance</h2>

        <div className="statistics-grid">
          {statistics.map((stat, index) => (
            <div key={index} className="statistic-item">
              <div className="statistic-value">{stat.value}</div>
              <p className="statistic-label">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatisticsSection;
