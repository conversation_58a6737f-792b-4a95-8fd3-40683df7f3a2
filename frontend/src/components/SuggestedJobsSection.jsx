import { Link } from 'react-router-dom';
import '../styles/components/suggested-jobs-section.css';

const SuggestedJobsSection = () => {
  const suggestedJobs = [
    {
      id: 1,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "teal"
    },
    {
      id: 2,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "purple"
    },
    {
      id: 3,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "dark-purple"
    },
    {
      id: 4,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "blue"
    },
    {
      id: 5,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "indigo"
    },
    {
      id: 6,
      title: "Senior UI Designer",
      description: "Join our platform and be part of our great UI team",
      location: "Kisumu, Kenya",
      salary: "30K Monthly",
      tags: ["Full time", "2yrs experience", "Kisumu, Kenya"],
      colorClass: "green"
    }
  ];

  return (
    <section className="suggested-jobs-section">
      <div className="suggested-container">
        <h2 className="suggested-title">Suggested Jobs</h2>

        <div className="suggested-grid">
          {suggestedJobs.map((job) => (
            <div key={job.id} className={`job-card job-card-${job.colorClass}`}>
              <h3 className="job-title">{job.title}</h3>
              <p className="job-description">{job.description}</p>
              
              <div className="job-details">
                <span className="job-location">{job.location}</span>
                <span className="job-salary">{job.salary}</span>
              </div>
              
              <div className="job-tags">
                {job.tags.map((tag, index) => (
                  <span key={index} className="job-tag">
                    {tag}
                  </span>
                ))}
              </div>
              
              <Link to="/register" className="btn btn-apply-job">
                Apply Now
              </Link>
            </div>
          ))}
        </div>

        {/* View All Jobs Button */}
        <div className="view-all-container">
          <Link to="/jobs" className="btn btn-view-all">
            View All Jobs
          </Link>
        </div>
      </div>
    </section>
  );
};

export default SuggestedJobsSection;
