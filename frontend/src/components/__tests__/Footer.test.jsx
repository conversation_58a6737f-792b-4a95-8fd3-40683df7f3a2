import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import Footer from '../Footer';

// Helper function to render component with router
const renderWithRouter = (component) => {
  return render(
    <Router>
      {component}
    </Router>
  );
};

describe('Footer', () => {
  it('renders the main CTA section', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByText('Discover Other Job Opportunities')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /explore all jobs/i })).toBeInTheDocument();
  });

  it('renders the company logo and description', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByText('JOBLINK')).toBeInTheDocument();
    expect(screen.getByText(/Get job alerts and career tips straight to your inbox/i)).toBeInTheDocument();
  });

  it('renders all job links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /latest listings/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /remote jobs/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /government jobs/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /tech jobs/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view all/i })).toBeInTheDocument();
  });

  it('renders all resource links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /blog/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /career tips/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /resume builder/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /faqs/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /contact us/i })).toBeInTheDocument();
  });

  it('renders all company links', () => {
    renderWithRouter(<Footer />);

    expect(screen.getByRole('link', { name: /about/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /our team/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /terms of use/i })).toBeInTheDocument();
    // Use getAllByRole for privacy policy since it appears multiple times
    const privacyLinks = screen.getAllByRole('link', { name: /privacy policy/i });
    expect(privacyLinks.length).toBeGreaterThan(0);
  });

  it('renders social media links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByText('X')).toBeInTheDocument();
    expect(screen.getByText('📷')).toBeInTheDocument();
    expect(screen.getByText('in')).toBeInTheDocument();
    expect(screen.getByText('▶')).toBeInTheDocument();
  });

  it('renders footer section headings', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('heading', { name: /jobs/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /resources/i })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: /company/i })).toBeInTheDocument();
  });

  it('renders copyright information', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByText('© 2025 JOBLINK. All rights reserved.')).toBeInTheDocument();
  });



  it('has correct link paths for job links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /latest listings/i })).toHaveAttribute('href', '/jobs');
    expect(screen.getByRole('link', { name: /remote jobs/i })).toHaveAttribute('href', '/jobs?type=remote');
    expect(screen.getByRole('link', { name: /government jobs/i })).toHaveAttribute('href', '/jobs?category=government');
    expect(screen.getByRole('link', { name: /tech jobs/i })).toHaveAttribute('href', '/jobs?category=tech');
  });

  it('has correct link paths for resource links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /blog/i })).toHaveAttribute('href', '/blog');
    expect(screen.getByRole('link', { name: /career tips/i })).toHaveAttribute('href', '/career-tips');
    expect(screen.getByRole('link', { name: /resume builder/i })).toHaveAttribute('href', '/resume-builder');
    expect(screen.getByRole('link', { name: /faqs/i })).toHaveAttribute('href', '/faqs');
    expect(screen.getByRole('link', { name: /contact us/i })).toHaveAttribute('href', '/contact');
  });

  it('has correct link paths for company links', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /about/i })).toHaveAttribute('href', '/about');
    expect(screen.getByRole('link', { name: /our team/i })).toHaveAttribute('href', '/team');
    expect(screen.getByRole('link', { name: /terms of use/i })).toHaveAttribute('href', '/terms');
  });

  it('explore all jobs button has correct path', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('link', { name: /explore all jobs/i })).toHaveAttribute('href', '/jobs');
  });

  it('has proper semantic HTML structure', () => {
    const { container } = renderWithRouter(<Footer />);
    
    expect(container.querySelector('footer')).toBeInTheDocument();
    expect(container.querySelector('.footer-container')).toBeInTheDocument();
    expect(container.querySelector('.footer-grid')).toBeInTheDocument();
    expect(container.querySelector('.footer-bottom')).toBeInTheDocument();
  });

  it('has correct CSS classes for main sections', () => {
    const { container } = renderWithRouter(<Footer />);
    
    expect(container.querySelector('.footer')).toBeInTheDocument();
    expect(container.querySelector('.footer-header')).toBeInTheDocument();
    expect(container.querySelector('.footer-company')).toBeInTheDocument();
    expect(container.querySelectorAll('.footer-column')).toHaveLength(3);
  });

  it('social links have correct CSS classes', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const socialLinks = container.querySelectorAll('.social-link');
    expect(socialLinks).toHaveLength(4);
    
    socialLinks.forEach(link => {
      expect(link).toHaveClass('social-link');
    });
  });

  it('footer links have correct CSS classes', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const footerLinks = container.querySelectorAll('.footer-link');
    expect(footerLinks.length).toBeGreaterThan(0);
    
    footerLinks.forEach(link => {
      expect(link).toHaveClass('footer-link');
    });
  });

  it('CTA button has correct CSS classes', () => {
    renderWithRouter(<Footer />);
    
    const ctaButton = screen.getByRole('link', { name: /explore all jobs/i });
    expect(ctaButton).toHaveClass('btn', 'btn-footer-cta');
  });

  it('renders as footer element', () => {
    renderWithRouter(<Footer />);
    
    expect(screen.getByRole('contentinfo')).toBeInTheDocument();
  });

  it('footer grid contains correct number of columns', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const footerGrid = container.querySelector('.footer-grid');
    const company = footerGrid.querySelector('.footer-company');
    const columns = footerGrid.querySelectorAll('.footer-column');
    
    expect(company).toBeInTheDocument();
    expect(columns).toHaveLength(3);
  });

  it('social links container exists', () => {
    const { container } = renderWithRouter(<Footer />);
    
    expect(container.querySelector('.social-links')).toBeInTheDocument();
  });

  it('footer bottom section is properly structured', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const footerBottom = container.querySelector('.footer-bottom');
    const bottomContainer = footerBottom.querySelector('.footer-bottom-container');
    const bottomContent = bottomContainer.querySelector('.footer-bottom-content');
    
    expect(footerBottom).toBeInTheDocument();
    expect(bottomContainer).toBeInTheDocument();
    expect(bottomContent).toBeInTheDocument();
  });

  it('footer logo has correct CSS class', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const logo = container.querySelector('.footer-logo');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveTextContent('JOBLINK');
  });

  it('footer description has correct CSS class', () => {
    const { container } = renderWithRouter(<Footer />);
    
    const description = container.querySelector('.footer-description');
    expect(description).toBeInTheDocument();
    expect(description).toHaveTextContent(/Get job alerts and career tips/i);
  });
});
