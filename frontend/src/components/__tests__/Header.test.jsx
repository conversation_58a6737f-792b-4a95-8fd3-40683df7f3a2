import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import Header from '../Header';

// Helper function to render component with router
const renderWithRouter = (component) => {
  return render(
    <Router>
      {component}
    </Router>
  );
};

describe('Header', () => {
  it('renders the logo correctly', () => {
    renderWithRouter(<Header />);
    
    expect(screen.getByText('JOBLINK')).toBeInTheDocument();
  });

  it('renders all navigation links', () => {
    renderWithRouter(<Header />);
    
    expect(screen.getByRole('link', { name: /home/<USER>
    expect(screen.getByRole('link', { name: /about us/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /jobs/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /faqs/i })).toBeInTheDocument();
  });

  it('renders authentication buttons', () => {
    renderWithRouter(<Header />);
    
    expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /register/i })).toBeInTheDocument();
  });

  it('has correct navigation link paths', () => {
    renderWithRouter(<Header />);
    
    expect(screen.getByRole('link', { name: /home/<USER>'href', '/');
    expect(screen.getByRole('link', { name: /about us/i })).toHaveAttribute('href', '/about');
    expect(screen.getByRole('link', { name: /jobs/i })).toHaveAttribute('href', '/jobs');
    expect(screen.getByRole('link', { name: /faqs/i })).toHaveAttribute('href', '/faqs');
  });

  it('has correct authentication button paths', () => {
    renderWithRouter(<Header />);
    
    expect(screen.getByRole('link', { name: /login/i })).toHaveAttribute('href', '/login');
    expect(screen.getByRole('link', { name: /register/i })).toHaveAttribute('href', '/register');
  });

  it('applies correct CSS classes', () => {
    renderWithRouter(<Header />);
    
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('header');
    
    const logo = screen.getByText('JOBLINK');
    expect(logo).toHaveClass('logo-text');
  });

  it('has proper semantic HTML structure', () => {
    renderWithRouter(<Header />);
    
    // Check for header element
    expect(screen.getByRole('banner')).toBeInTheDocument();
    
    // Check for navigation element
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('navigation links have proper accessibility attributes', () => {
    renderWithRouter(<Header />);
    
    const navLinks = screen.getAllByRole('link');
    navLinks.forEach(link => {
      expect(link).toHaveAttribute('href');
    });
  });

  it('renders with correct container structure', () => {
    const { container } = renderWithRouter(<Header />);
    
    expect(container.querySelector('.header-container')).toBeInTheDocument();
    expect(container.querySelector('.header-content')).toBeInTheDocument();
    expect(container.querySelector('.logo')).toBeInTheDocument();
    expect(container.querySelector('.nav')).toBeInTheDocument();
    expect(container.querySelector('.auth-buttons')).toBeInTheDocument();
  });

  it('auth buttons have correct CSS classes', () => {
    renderWithRouter(<Header />);
    
    const loginButton = screen.getByRole('link', { name: /login/i });
    const registerButton = screen.getByRole('link', { name: /register/i });
    
    expect(loginButton).toHaveClass('btn', 'btn-auth');
    expect(registerButton).toHaveClass('btn', 'btn-auth');
  });

  it('navigation links have correct CSS classes', () => {
    renderWithRouter(<Header />);
    
    const homeLink = screen.getByRole('link', { name: /home/<USER>
    const aboutLink = screen.getByRole('link', { name: /about us/i });
    const jobsLink = screen.getByRole('link', { name: /jobs/i });
    const faqsLink = screen.getByRole('link', { name: /faqs/i });
    
    expect(homeLink).toHaveClass('nav-link');
    expect(aboutLink).toHaveClass('nav-link');
    expect(jobsLink).toHaveClass('nav-link');
    expect(faqsLink).toHaveClass('nav-link');
  });
});
