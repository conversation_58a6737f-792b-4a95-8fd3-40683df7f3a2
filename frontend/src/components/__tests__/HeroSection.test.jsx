import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe, vi } from 'vitest';
import HeroSection from '../HeroSection';

// Helper function to render component with router
const renderWithRouter = (component) => {
  return render(
    <Router>
      {component}
    </Router>
  );
};

describe('HeroSection', () => {
  it('renders the main heading correctly', () => {
    renderWithRouter(<HeroSection />);

    expect(screen.getByText(/we help you find/i)).toBeInTheDocument();
    expect(screen.getByText(/dream job/i)).toBeInTheDocument();
  });

  it('renders the description text', () => {
    renderWithRouter(<HeroSection />);
    
    expect(screen.getByText(/Join our platform to be paired with one of our top skilled talent with global tech/i)).toBeInTheDocument();
  });

  it('renders the Apply Now button with correct link', () => {
    renderWithRouter(<HeroSection />);
    
    const applyButton = screen.getByRole('link', { name: /apply now/i });
    expect(applyButton).toBeInTheDocument();
    expect(applyButton).toHaveAttribute('href', '/register');
    expect(applyButton).toHaveClass('btn', 'btn-apply');
  });

  it('renders the search form with all fields', () => {
    renderWithRouter(<HeroSection />);
    
    // Check for job title input
    expect(screen.getByPlaceholderText(/job title keyword/i)).toBeInTheDocument();
    
    // Check for location select
    expect(screen.getByDisplayValue('')).toBeInTheDocument();
    
    // Check for search button
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
  });

  it('allows user to type in job title input', () => {
    renderWithRouter(<HeroSection />);
    
    const jobTitleInput = screen.getByPlaceholderText(/job title keyword/i);
    fireEvent.change(jobTitleInput, { target: { value: 'Software Engineer' } });
    
    expect(jobTitleInput.value).toBe('Software Engineer');
  });

  it('allows user to select location', () => {
    renderWithRouter(<HeroSection />);
    
    const locationSelect = screen.getByRole('combobox');
    fireEvent.change(locationSelect, { target: { value: 'kisumu' } });
    
    expect(locationSelect.value).toBe('kisumu');
  });

  it('has all location options in select', () => {
    renderWithRouter(<HeroSection />);
    
    expect(screen.getByRole('option', { name: /select area/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /kisumu/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /nairobi/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /mombasa/i })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: /remote/i })).toBeInTheDocument();
  });

  it('handles form submission', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    renderWithRouter(<HeroSection />);
    
    const jobTitleInput = screen.getByPlaceholderText(/job title keyword/i);
    const locationSelect = screen.getByRole('combobox');
    const searchButton = screen.getByRole('button', { name: /search/i });
    
    fireEvent.change(jobTitleInput, { target: { value: 'Developer' } });
    fireEvent.change(locationSelect, { target: { value: 'nairobi' } });
    fireEvent.click(searchButton);
    
    expect(consoleSpy).toHaveBeenCalledWith('Searching for:', { jobTitle: 'Developer', location: 'nairobi' });
    
    consoleSpy.mockRestore();
  });



  it('renders the professional image section', () => {
    renderWithRouter(<HeroSection />);

    const heroImage = screen.getByAltText(/professional team working together/i);
    expect(heroImage).toBeInTheDocument();
    expect(heroImage).toHaveAttribute('src', '/heroimage.png');
    expect(heroImage).toHaveClass('hero-main-image');
  });

  it('has correct CSS classes for styling', () => {
    const { container } = renderWithRouter(<HeroSection />);
    
    expect(container.querySelector('.hero-container')).toBeInTheDocument();
    expect(container.querySelector('.hero-grid')).toBeInTheDocument();
    expect(container.querySelector('.hero-content')).toBeInTheDocument();
    expect(container.querySelector('.hero-text')).toBeInTheDocument();
    expect(container.querySelector('.search-form')).toBeInTheDocument();
    expect(container.querySelector('.hero-image')).toBeInTheDocument();
  });

  it('has proper semantic HTML structure', () => {
    renderWithRouter(<HeroSection />);
    
    // Check for main element
    expect(screen.getByRole('main')).toBeInTheDocument();
    
    // Check for form element
    const searchButton = screen.getByRole('button', { name: /search/i });
    expect(searchButton.closest('form')).toBeInTheDocument();
  });

  it('search form has correct grid layout classes', () => {
    const { container } = renderWithRouter(<HeroSection />);
    
    expect(container.querySelector('.search-grid')).toBeInTheDocument();
    expect(container.querySelectorAll('.search-field')).toHaveLength(2);
  });

  it('search icons are present', () => {
    renderWithRouter(<HeroSection />);
    
    expect(screen.getByText('🔍')).toBeInTheDocument();
    expect(screen.getByText('📍')).toBeInTheDocument();
  });

  it('apply button has arrow icon', () => {
    renderWithRouter(<HeroSection />);
    
    expect(screen.getByText('→')).toBeInTheDocument();
  });

  it('handles empty form submission', () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    renderWithRouter(<HeroSection />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);
    
    expect(consoleSpy).toHaveBeenCalledWith('Searching for:', { jobTitle: '', location: '' });
    
    consoleSpy.mockRestore();
  });

  it('maintains state between interactions', () => {
    renderWithRouter(<HeroSection />);
    
    const jobTitleInput = screen.getByPlaceholderText(/job title keyword/i);
    const locationSelect = screen.getByRole('combobox');
    
    // Set values
    fireEvent.change(jobTitleInput, { target: { value: 'Designer' } });
    fireEvent.change(locationSelect, { target: { value: 'kisumu' } });
    
    // Values should persist
    expect(jobTitleInput.value).toBe('Designer');
    expect(locationSelect.value).toBe('kisumu');
    
    // Change values again
    fireEvent.change(jobTitleInput, { target: { value: 'Manager' } });
    fireEvent.change(locationSelect, { target: { value: 'remote' } });
    
    expect(jobTitleInput.value).toBe('Manager');
    expect(locationSelect.value).toBe('remote');
  });
});
