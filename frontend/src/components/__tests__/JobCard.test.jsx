import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import JobCard from '../JobCard';

describe('JobCard', () => {
  const mockJob = {
    title: 'Software Engineer',
    company: 'Tech Solutions Inc.',
    location: 'Milimani, Kisumu',
    type: 'Full-time',
    experience: '> 2 y experience',
    salary: 'Ksh 45,000/monthly',
    deadline: '23/Aug/2025',
  };

  it('renders job details correctly', () => {
    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );

    expect(screen.getByText(/Software Engineer/i)).toBeInTheDocument();
    // Removed assertions for company and description as they are no longer directly rendered
    // expect(screen.getByText(/Tech Solutions Inc\./i)).toBeInTheDocument();
    expect(screen.getByText(/Milimani, Kisumu/i)).toBeInTheDocument();
    expect(screen.getByText(/Full-time/i)).toBeInTheDocument();
    expect(screen.getByText(/> 2 y experience/i)).toBeInTheDocument();
    expect(screen.getByText(/Ksh 45,000\/monthly/i)).toBeInTheDocument();
  });

  it('displays the deadline with the correct text', () => {
    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );
    expect(screen.getByText(/Due on : 23\/Aug\/2025/i)).toBeInTheDocument();
  });

  it('applies the correct deadline status class (new)', () => {
    // Mocking Date for consistent testing of deadline logic
    const mockDate = new Date('2025-07-01T12:00:00.000Z'); // More than 30 days before 23/Aug/2025
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );

    const deadlineElement = screen.getByText(/Due on :/i).closest('.job-badge');
    expect(deadlineElement).toHaveClass('deadline-new');

    vi.useRealTimers();
  });

  it('applies the correct deadline status class (almost-due)', () => {
    const mockDate = new Date('2025-08-10T12:00:00.000Z'); // Between 7 and 30 days before 23/Aug/2025
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );

    const deadlineElement = screen.getByText(/Due on :/i).closest('.job-badge');
    expect(deadlineElement).toHaveClass('deadline-almost-due');

    vi.useRealTimers();
  });

  it('applies the correct deadline status class (very-due)', () => {
    const mockDate = new Date('2025-08-20T12:00:00.000Z'); // Within 7 days before 23/Aug/2025
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );

    const deadlineElement = screen.getByText(/Due on :/i).closest('.job-badge');
    expect(deadlineElement).toHaveClass('deadline-very-due');

    vi.useRealTimers();
  });

  it('applies the correct deadline status class (passed)', () => {
    const mockDate = new Date('2025-08-24T12:00:00.000Z'); // After 23/Aug/2025
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );

    const deadlineElement = screen.getByText(/Due on :/i).closest('.job-badge');
    expect(deadlineElement).toHaveClass('deadline-passed');

    vi.useRealTimers();
  });

  it('renders the Apply now button and links to /register', () => {
    render(
      <Router>
        <JobCard job={mockJob} />
      </Router>
    );
    const applyButton = screen.getByRole('link', { name: /Apply now/i });
    expect(applyButton).toBeInTheDocument();
    expect(applyButton).toHaveAttribute('href', '/register');
  });
});