import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import JobCategoriesSection from '../JobCategoriesSection';

// Helper function to render component with router
const renderWithRouter = (component) => {
  return render(
    <Router>
      {component}
    </Router>
  );
};

describe('JobCategoriesSection', () => {
  it('renders all tab buttons', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    expect(screen.getByRole('button', { name: /jobs by department/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /jobs by location/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /popular jobs/i })).toBeInTheDocument();
  });

  it('has department tab active by default', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const departmentTab = screen.getByRole('button', { name: /jobs by department/i });
    expect(departmentTab).toHaveClass('active');
  });

  it('switches to location tab when clicked', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    fireEvent.click(locationTab);
    
    expect(locationTab).toHaveClass('active');
  });

  it('switches to popular tab when clicked', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    expect(popularTab).toHaveClass('active');
  });

  it('displays department content by default', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    expect(screen.getByText('Engineering & Development')).toBeInTheDocument();
    expect(screen.getByText('Design & Creative')).toBeInTheDocument();
    expect(screen.getByText('Marketing & Communications')).toBeInTheDocument();
    expect(screen.getByText('Customer Support & Success')).toBeInTheDocument();
  });

  it('displays department job links', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    expect(screen.getByRole('link', { name: /frontend developer/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /backend developer/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /ux designer/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /ui designer/i })).toBeInTheDocument();
  });

  it('displays location content when location tab is active', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    fireEvent.click(locationTab);
    
    expect(screen.getByText('Kisumu, Kenya')).toBeInTheDocument();
    expect(screen.getByText('Nairobi, Kenya')).toBeInTheDocument();
    expect(screen.getByText('Mombasa, Kenya')).toBeInTheDocument();
    expect(screen.getByText('Remote')).toBeInTheDocument();
  });

  it('displays job counts for locations', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    fireEvent.click(locationTab);
    
    expect(screen.getByText('1,234 jobs available')).toBeInTheDocument();
    expect(screen.getByText('987 jobs available')).toBeInTheDocument();
    expect(screen.getByText('756 jobs available')).toBeInTheDocument();
    expect(screen.getByText('2,145 jobs available')).toBeInTheDocument();
  });

  it('displays popular jobs content when popular tab is active', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
    expect(screen.getByText('Product Manager')).toBeInTheDocument();
    expect(screen.getByText('UX Designer')).toBeInTheDocument();
    expect(screen.getByText('Data Scientist')).toBeInTheDocument();
  });

  it('displays job badges in popular jobs', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    expect(screen.getByText('Hot')).toBeInTheDocument();
    expect(screen.getByText('Trending')).toBeInTheDocument();
    expect(screen.getByText('Popular')).toBeInTheDocument();
    expect(screen.getByText('Growing')).toBeInTheDocument();
  });

  it('displays job openings count in popular jobs', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    expect(screen.getByText('345 openings')).toBeInTheDocument();
    expect(screen.getByText('128 openings')).toBeInTheDocument();
    expect(screen.getByText('89 openings')).toBeInTheDocument();
    expect(screen.getByText('156 openings')).toBeInTheDocument();
  });

  it('has view jobs buttons in location tab', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    fireEvent.click(locationTab);
    
    const viewJobsButtons = screen.getAllByRole('link', { name: /view jobs/i });
    expect(viewJobsButtons).toHaveLength(4);
    
    viewJobsButtons.forEach(button => {
      expect(button).toHaveAttribute('href', '/jobs');
    });
  });

  it('has apply buttons in popular jobs tab', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    const applyButtons = screen.getAllByRole('link', { name: /apply/i });
    expect(applyButtons).toHaveLength(4);
    
    applyButtons.forEach(button => {
      expect(button).toHaveAttribute('href', '/jobs');
    });
  });

  it('has correct CSS classes for tabs', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const departmentTab = screen.getByRole('button', { name: /jobs by department/i });
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    
    expect(departmentTab).toHaveClass('tab-button', 'active');
    expect(locationTab).toHaveClass('tab-button');
    expect(popularTab).toHaveClass('tab-button');
  });

  it('has proper semantic HTML structure', () => {
    const { container } = renderWithRouter(<JobCategoriesSection />);
    
    expect(container.querySelector('section')).toBeInTheDocument();
    expect(container.querySelector('.categories-container')).toBeInTheDocument();
    expect(container.querySelector('.tab-navigation')).toBeInTheDocument();
    expect(container.querySelector('.tab-content')).toBeInTheDocument();
  });

  it('location tab has map pin icon', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    expect(screen.getByText('📍')).toBeInTheDocument();
  });

  it('department job links point to jobs page', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const frontendLink = screen.getByRole('link', { name: /frontend developer/i });
    expect(frontendLink).toHaveAttribute('href', '/jobs');
  });

  it('only shows one tab content at a time', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    // Initially department content is visible
    expect(screen.getByText('Engineering & Development')).toBeInTheDocument();
    expect(screen.queryByText('Kisumu, Kenya')).not.toBeInTheDocument();
    expect(screen.queryByText('Software Engineer')).not.toBeInTheDocument();
    
    // Switch to location tab
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    fireEvent.click(locationTab);
    
    expect(screen.queryByText('Engineering & Development')).not.toBeInTheDocument();
    expect(screen.getByText('Kisumu, Kenya')).toBeInTheDocument();
    expect(screen.queryByText('Software Engineer')).not.toBeInTheDocument();
    
    // Switch to popular tab
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    fireEvent.click(popularTab);
    
    expect(screen.queryByText('Engineering & Development')).not.toBeInTheDocument();
    expect(screen.queryByText('Kisumu, Kenya')).not.toBeInTheDocument();
    expect(screen.getByText('Software Engineer')).toBeInTheDocument();
  });

  it('maintains tab state correctly', () => {
    renderWithRouter(<JobCategoriesSection />);
    
    const departmentTab = screen.getByRole('button', { name: /jobs by department/i });
    const locationTab = screen.getByRole('button', { name: /jobs by location/i });
    const popularTab = screen.getByRole('button', { name: /popular jobs/i });
    
    // Switch to location
    fireEvent.click(locationTab);
    expect(locationTab).toHaveClass('active');
    expect(departmentTab).not.toHaveClass('active');
    expect(popularTab).not.toHaveClass('active');
    
    // Switch to popular
    fireEvent.click(popularTab);
    expect(popularTab).toHaveClass('active');
    expect(departmentTab).not.toHaveClass('active');
    expect(locationTab).not.toHaveClass('active');
    
    // Switch back to department
    fireEvent.click(departmentTab);
    expect(departmentTab).toHaveClass('active');
    expect(locationTab).not.toHaveClass('active');
    expect(popularTab).not.toHaveClass('active');
  });
});
