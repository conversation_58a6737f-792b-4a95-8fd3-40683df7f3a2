import { render, screen } from '@testing-library/react';
import { expect, it, describe } from 'vitest';
import StatisticsSection from '../StatisticsSection';

describe('StatisticsSection', () => {
  it('renders the section title', () => {
    render(<StatisticsSection />);
    
    expect(screen.getByText('Statistics at a Glance')).toBeInTheDocument();
  });

  it('renders all statistic values', () => {
    render(<StatisticsSection />);
    
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('1200')).toBeInTheDocument();
    expect(screen.getByText('95%')).toBeInTheDocument();
  });

  it('renders all statistic labels', () => {
    render(<StatisticsSection />);
    
    expect(screen.getByText('Jobs Available')).toBeInTheDocument();
    expect(screen.getByText('Applications Processed')).toBeInTheDocument();
    expect(screen.getByText('User Satisfaction')).toBeInTheDocument();
  });

  it('has correct number of statistic items', () => {
    const { container } = render(<StatisticsSection />);
    
    const statisticItems = container.querySelectorAll('.statistic-item');
    expect(statisticItems).toHaveLength(3);
  });

  it('has proper semantic HTML structure', () => {
    const { container } = render(<StatisticsSection />);
    
    expect(container.querySelector('section')).toBeInTheDocument();
    expect(container.querySelector('.statistics-container')).toBeInTheDocument();
    expect(container.querySelector('.statistics-grid')).toBeInTheDocument();
  });

  it('section title has correct CSS class', () => {
    const { container } = render(<StatisticsSection />);
    
    const title = container.querySelector('.statistics-title');
    expect(title).toBeInTheDocument();
    expect(title).toHaveTextContent('Statistics at a Glance');
  });

  it('statistic values have correct CSS classes', () => {
    const { container } = render(<StatisticsSection />);
    
    const values = container.querySelectorAll('.statistic-value');
    expect(values).toHaveLength(3);
    
    expect(values[0]).toHaveTextContent('85%');
    expect(values[1]).toHaveTextContent('1200');
    expect(values[2]).toHaveTextContent('95%');
  });

  it('statistic labels have correct CSS classes', () => {
    const { container } = render(<StatisticsSection />);
    
    const labels = container.querySelectorAll('.statistic-label');
    expect(labels).toHaveLength(3);
    
    expect(labels[0]).toHaveTextContent('Jobs Available');
    expect(labels[1]).toHaveTextContent('Applications Processed');
    expect(labels[2]).toHaveTextContent('User Satisfaction');
  });

  it('each statistic item contains both value and label', () => {
    const { container } = render(<StatisticsSection />);
    
    const statisticItems = container.querySelectorAll('.statistic-item');
    
    statisticItems.forEach(item => {
      expect(item.querySelector('.statistic-value')).toBeInTheDocument();
      expect(item.querySelector('.statistic-label')).toBeInTheDocument();
    });
  });

  it('statistics are displayed in correct order', () => {
    const { container } = render(<StatisticsSection />);
    
    const statisticItems = container.querySelectorAll('.statistic-item');
    
    // First statistic: Jobs Available
    expect(statisticItems[0].querySelector('.statistic-value')).toHaveTextContent('85%');
    expect(statisticItems[0].querySelector('.statistic-label')).toHaveTextContent('Jobs Available');
    
    // Second statistic: Applications Processed
    expect(statisticItems[1].querySelector('.statistic-value')).toHaveTextContent('1200');
    expect(statisticItems[1].querySelector('.statistic-label')).toHaveTextContent('Applications Processed');
    
    // Third statistic: User Satisfaction
    expect(statisticItems[2].querySelector('.statistic-value')).toHaveTextContent('95%');
    expect(statisticItems[2].querySelector('.statistic-label')).toHaveTextContent('User Satisfaction');
  });

  it('section has correct CSS class for styling', () => {
    const { container } = render(<StatisticsSection />);
    
    const section = container.querySelector('.statistics-section');
    expect(section).toBeInTheDocument();
  });

  it('grid layout is properly structured', () => {
    const { container } = render(<StatisticsSection />);
    
    const grid = container.querySelector('.statistics-grid');
    expect(grid).toBeInTheDocument();
    
    const items = grid.querySelectorAll('.statistic-item');
    expect(items).toHaveLength(3);
  });



  it('title is properly structured as heading', () => {
    render(<StatisticsSection />);
    
    const title = screen.getByRole('heading', { level: 2 });
    expect(title).toHaveTextContent('Statistics at a Glance');
    expect(title).toHaveClass('statistics-title');
  });

  it('statistic labels are properly structured as paragraphs', () => {
    const { container } = render(<StatisticsSection />);
    
    const labels = container.querySelectorAll('.statistic-label');
    
    labels.forEach(label => {
      expect(label.tagName.toLowerCase()).toBe('p');
    });
  });

  it('container structure is correct', () => {
    const { container } = render(<StatisticsSection />);
    
    const section = container.querySelector('.statistics-section');
    const containerDiv = section.querySelector('.statistics-container');
    const title = containerDiv.querySelector('.statistics-title');
    const grid = containerDiv.querySelector('.statistics-grid');
    
    expect(section).toBeInTheDocument();
    expect(containerDiv).toBeInTheDocument();
    expect(title).toBeInTheDocument();
    expect(grid).toBeInTheDocument();
  });

  it('statistics data is correctly structured', () => {
    render(<StatisticsSection />);
    
    // Test that the component renders the expected statistics
    const expectedStats = [
      { value: '85%', label: 'Jobs Available' },
      { value: '1200', label: 'Applications Processed' },
      { value: '95%', label: 'User Satisfaction' }
    ];
    
    expectedStats.forEach(stat => {
      expect(screen.getByText(stat.value)).toBeInTheDocument();
      expect(screen.getByText(stat.label)).toBeInTheDocument();
    });
  });
});
