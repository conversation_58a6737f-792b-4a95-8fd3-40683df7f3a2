import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import SuggestedJobsSection from '../SuggestedJobsSection';

// Helper function to render component with router
const renderWithRouter = (component) => {
  return render(
    <Router>
      {component}
    </Router>
  );
};

describe('SuggestedJobsSection', () => {
  it('renders the section title', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    expect(screen.getByText('Suggested Jobs')).toBeInTheDocument();
  });

  it('renders all job cards', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const jobTitles = screen.getAllByText('Senior UI Designer');
    expect(jobTitles).toHaveLength(6);
  });

  it('renders job descriptions', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const descriptions = screen.getAllByText('Join our platform and be part of our great UI team');
    expect(descriptions).toHaveLength(6);
  });

  it('renders job locations', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const locations = screen.getAllByText('Kisumu, Kenya');
    expect(locations).toHaveLength(12); // 6 in location field + 6 in tags
  });

  it('renders job salaries', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const salaries = screen.getAllByText('30K Monthly');
    expect(salaries).toHaveLength(6);
  });

  it('renders job tags for each card', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const fullTimeTags = screen.getAllByText('Full time');
    const experienceTags = screen.getAllByText('2yrs experience');
    const locationTags = screen.getAllByText('Kisumu, Kenya');
    
    expect(fullTimeTags).toHaveLength(6);
    expect(experienceTags).toHaveLength(6);
    expect(locationTags).toHaveLength(12); // 6 in details + 6 in tags
  });

  it('renders apply now buttons for each job', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const applyButtons = screen.getAllByRole('link', { name: /apply now/i });
    expect(applyButtons).toHaveLength(6);
    
    applyButtons.forEach(button => {
      expect(button).toHaveAttribute('href', '/register');
    });
  });

  it('renders view all jobs button', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const viewAllButton = screen.getByRole('link', { name: /view all jobs/i });
    expect(viewAllButton).toBeInTheDocument();
    expect(viewAllButton).toHaveAttribute('href', '/jobs');
  });

  it('has correct CSS classes for different colored job cards', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    expect(container.querySelector('.job-card-teal')).toBeInTheDocument();
    expect(container.querySelector('.job-card-purple')).toBeInTheDocument();
    expect(container.querySelector('.job-card-dark-purple')).toBeInTheDocument();
    expect(container.querySelector('.job-card-blue')).toBeInTheDocument();
    expect(container.querySelector('.job-card-indigo')).toBeInTheDocument();
    expect(container.querySelector('.job-card-green')).toBeInTheDocument();
  });

  it('has proper semantic HTML structure', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    expect(container.querySelector('section')).toBeInTheDocument();
    expect(container.querySelector('.suggested-container')).toBeInTheDocument();
    expect(container.querySelector('.suggested-grid')).toBeInTheDocument();
    expect(container.querySelector('.view-all-container')).toBeInTheDocument();
  });

  it('job cards have correct structure', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const jobCards = container.querySelectorAll('.job-card');
    expect(jobCards).toHaveLength(6);
    
    jobCards.forEach(card => {
      expect(card.querySelector('.job-title')).toBeInTheDocument();
      expect(card.querySelector('.job-description')).toBeInTheDocument();
      expect(card.querySelector('.job-details')).toBeInTheDocument();
      expect(card.querySelector('.job-tags')).toBeInTheDocument();
      expect(card.querySelector('.btn-apply-job')).toBeInTheDocument();
    });
  });

  it('job details section contains location and salary', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const jobDetails = container.querySelectorAll('.job-details');
    expect(jobDetails).toHaveLength(6);
    
    jobDetails.forEach(detail => {
      expect(detail.querySelector('.job-location')).toBeInTheDocument();
      expect(detail.querySelector('.job-salary')).toBeInTheDocument();
    });
  });

  it('job tags section contains all required tags', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const jobTagsContainers = container.querySelectorAll('.job-tags');
    expect(jobTagsContainers).toHaveLength(6);
    
    jobTagsContainers.forEach(tagsContainer => {
      const tags = tagsContainer.querySelectorAll('.job-tag');
      expect(tags).toHaveLength(3);
    });
  });

  it('section title has correct CSS class', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const title = container.querySelector('.suggested-title');
    expect(title).toBeInTheDocument();
    expect(title).toHaveTextContent('Suggested Jobs');
  });

  it('view all button has correct CSS classes', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const viewAllButton = screen.getByRole('link', { name: /view all jobs/i });
    expect(viewAllButton).toHaveClass('btn', 'btn-view-all');
  });

  it('apply buttons have correct CSS classes', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const applyButtons = screen.getAllByRole('link', { name: /apply now/i });
    applyButtons.forEach(button => {
      expect(button).toHaveClass('btn', 'btn-apply-job');
    });
  });

  it('renders correct number of job cards with unique IDs', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    // Check that we have exactly 6 job cards
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    const jobCards = container.querySelectorAll('.job-card');
    expect(jobCards).toHaveLength(6);
    
    // Check that each card has a different color class
    const colorClasses = [
      'job-card-teal',
      'job-card-purple', 
      'job-card-dark-purple',
      'job-card-blue',
      'job-card-indigo',
      'job-card-green'
    ];
    
    colorClasses.forEach(colorClass => {
      expect(container.querySelector(`.${colorClass}`)).toBeInTheDocument();
    });
  });

  it('job titles are properly structured as headings', () => {
    renderWithRouter(<SuggestedJobsSection />);
    
    const jobTitles = screen.getAllByRole('heading', { level: 3 });
    expect(jobTitles).toHaveLength(6);
    
    jobTitles.forEach(title => {
      expect(title).toHaveTextContent('Senior UI Designer');
      expect(title).toHaveClass('job-title');
    });
  });

  it('section has correct background styling', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const section = container.querySelector('.suggested-jobs-section');
    expect(section).toBeInTheDocument();
  });

  it('grid layout is properly structured', () => {
    const { container } = renderWithRouter(<SuggestedJobsSection />);
    
    const grid = container.querySelector('.suggested-grid');
    expect(grid).toBeInTheDocument();
    
    const jobCards = grid.querySelectorAll('.job-card');
    expect(jobCards).toHaveLength(6);
  });
});
