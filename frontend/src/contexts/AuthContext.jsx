import { createContext, useState, useEffect } from 'react';

const AuthContext = createContext();
/**
 * Authentication Provider Component
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = Boolean(user && token);

  // Initialize authentication state from localStorage
  useEffect(() => {
    try {
      const storedToken = localStorage.getItem('authToken');
      const storedUser = localStorage.getItem('authUser');

      if (storedToken && storedUser) {
        const parsedUser = JSON.parse(storedUser);
        setToken(storedToken);
        setUser(parsedUser);
      }
    } catch (error) {
      console.error('Error loading authentication data from localStorage:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('authUser');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Login function - sets user data and token after successful authentication
  const login = (userData, authToken) => {
    try {
      // Relaxing the userData requirement if backend doesn't send it with login
      // AuthToken is still mandatory
      if (!authToken) {
        throw new Error('Authentication token is required for login');
      }

      setUser(userData || null); // Set user data if provided, otherwise null
      setToken(authToken);

      localStorage.setItem('authToken', authToken);
      if (userData) {
        localStorage.setItem('authUser', JSON.stringify(userData));
      } else {
        localStorage.removeItem('authUser'); // Clear if no user data provided
      }

      console.log('User logged in successfully:', userData ? (userData.email || userData.username) : 'Token received');
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  };

  // Clears all authentication state and localStorage
  const logout = () => {
    try {
      setUser(null);
      setToken(null);

      localStorage.removeItem('authToken');
      localStorage.removeItem('authUser');

      console.log('User logged out successfully');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Updates user data without affecting authentication token
  const updateUser = (updatedUserData) => {
    try {
      if (!updatedUserData) {
        throw new Error('Updated user data is required');
      }

      setUser(updatedUserData);
      localStorage.setItem('authUser', JSON.stringify(updatedUserData));

      console.log('User data updated successfully');
    } catch (error) {
      console.error('Error updating user data:', error);
      throw error;
    }
  };

  const contextValue = {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
