import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { expect, it, describe } from 'vitest';
import Home from '../Home';

describe('Home', () => {
  it('renders the main homepage structure', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check that the main container is rendered
    const homeContainer = document.querySelector('.home-container');
    expect(homeContainer).toBeInTheDocument();
  });

  it('renders the header component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for header content - use getAllByText for multiple instances
    const jobLinkTexts = screen.getAllByText('JOBLINK');
    expect(jobLinkTexts.length).toBeGreaterThan(0);
    expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /register/i })).toBeInTheDocument();
  });

  it('renders the hero section component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for hero section content - using more flexible text matching
    expect(screen.getByText(/we help you find/i)).toBeInTheDocument();
    expect(screen.getByText(/dream job/i)).toBeInTheDocument();
    // Check for apply now links (there are multiple, so use getAllByRole)
    const applyLinks = screen.getAllByRole('link', { name: /apply now/i });
    expect(applyLinks.length).toBeGreaterThan(0);
  });

  it('renders the job categories section component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for job categories tabs
    expect(screen.getByRole('button', { name: /jobs by department/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /jobs by location/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /popular jobs/i })).toBeInTheDocument();
  });

  it('renders the suggested jobs section component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for suggested jobs section
    expect(screen.getByText('Suggested Jobs')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view all jobs/i })).toBeInTheDocument();
  });

  it('renders the statistics section component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for statistics section
    expect(screen.getByText('Statistics at a Glance')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('1200')).toBeInTheDocument();
    expect(screen.getByText('95%')).toBeInTheDocument();
  });

  it('renders the footer component', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for footer content
    expect(screen.getByText('Discover Other Job Opportunities')).toBeInTheDocument();
    expect(screen.getByText('© 2025 JOBLINK. All rights reserved.')).toBeInTheDocument();
  });

  it('has proper semantic structure', () => {
    render(
      <Router>
        <Home />
      </Router>
    );

    // Check for semantic elements
    expect(screen.getByRole('banner')).toBeInTheDocument(); // Header
    expect(screen.getByRole('main')).toBeInTheDocument(); // Hero section
    expect(screen.getByRole('contentinfo')).toBeInTheDocument(); // Footer
  });
});