/* eslint-env jsdom, node */

import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Login from '../Login'
import { useAuth } from '../../contexts/useAuth'

// Mocks
const mockLogin = vi.fn()
const mockNavigate = vi.fn()

vi.mock('../../contexts/useAuth', () => ({
  useAuth: vi.fn()
}))

vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
  Link: ({ children, to }) => <a href={to}>{children}</a>
}))

describe('Login Component', () => {
  beforeEach(() => {
    useAuth.mockReturnValue({ login: mockLogin })
    global.fetch = vi.fn()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  test('renders all core elements correctly', () => {
    render(<Login />)
    expect(screen.getByRole('heading', { name: /login/i })).toBeInTheDocument()
    expect(screen.getByText(/access your kisumu county/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /register here/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /back to home/i })).toBeInTheDocument()
  })

  test('submit button is enabled by default (only disabled during loading)', () => {
    render(<Login />)
    const button = screen.getByRole('button', { name: /login/i })
    expect(button).not.toBeDisabled()
  })

  test('toggles password visibility correctly', async () => {
    const user = userEvent.setup()
    render(<Login />)

    const passwordInput = screen.getByLabelText(/^password$/i)
    const toggleBtn = screen.getByRole('button', { name: /show password/i })

    expect(passwordInput).toHaveAttribute('type', 'password')

    await user.click(toggleBtn)
    expect(passwordInput).toHaveAttribute('type', 'text')
    expect(toggleBtn).toHaveAttribute('aria-label', 'Hide password')

    await user.click(toggleBtn)
    expect(passwordInput).toHaveAttribute('type', 'password')
    expect(toggleBtn).toHaveAttribute('aria-label', 'Show password')
  })

  test('allows form submission with any email format (backend validates)', async () => {
    const user = userEvent.setup()
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        user: { id: 1, name: 'Alice' },
        authentication_token: 'abc123' // Corrected token key
      })
    })

    render(<Login />)

    const emailInput = screen.getByLabelText(/email address/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const button = screen.getByRole('button', { name: /login/i })

    // Test with various email formats - all should be allowed to submit
    await user.type(emailInput, 'user@domain')
    await user.type(passwordInput, 'password123')
    await user.click(button)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/jobuser/authenticate', { // Corrected endpoint
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: 'user@domain', password: 'password123' })
      })
    })
  })

  test('handles input changes correctly', async () => {
    const user = userEvent.setup()
    render(<Login />)

    const emailInput = screen.getByLabelText(/email address/i)
    const passwordInput = screen.getByLabelText(/^password$/i)

    await user.type(emailInput, '<EMAIL>')
    expect(emailInput).toHaveValue('<EMAIL>')

    await user.type(passwordInput, 'mypassword')
    expect(passwordInput).toHaveValue('mypassword')

    // Clear and retype
    await user.clear(emailInput)
    await user.type(emailInput, '<EMAIL>')
    expect(emailInput).toHaveValue('<EMAIL>')
  })

  test('successful login triggers auth and redirects', async () => {
    const user = userEvent.setup()
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        user: { id: 1, name: 'Alice' },
        authentication_token: 'abc123' // Corrected token key
      })
    })

    render(<Login />)

    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'password123')
    await user.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith(
        { id: 1, name: 'Alice' },
        'abc123'
      )
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
    })
  })

  test('displays fallback error on login failure', async () => {
    const user = userEvent.setup()
    global.fetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: 'Invalid credentials' })
    })

    render(<Login />)

    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'wrongpass')
    await user.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      // Expecting the error message from backend (Invalid credentials)
      expect(screen.getByText(/Invalid credentials/i)).toBeInTheDocument();
    })
  })

  test('handles fetch/network error gracefully', async () => {
    const user = userEvent.setup()
    global.fetch.mockRejectedValueOnce(new Error('Network error'))

    render(<Login />)

    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'somepass')
    await user.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      // Expecting the error message to be from network error
      expect(screen.getByText(/Network error/i)).toBeInTheDocument();
    })
  })

  test('disables submit button during loading', async () => {
    const user = userEvent.setup()
    
    // Mock a slow response to test loading state
    global.fetch.mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ 
          ok: true, 
          json: () => Promise.resolve({ user: { id: 1 }, token: 'token' }) 
        }), 100)
      )
    )

    render(<Login />)

    const emailInput = screen.getByLabelText(/email address/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const submitBtn = screen.getByRole('button', { name: /login/i })

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    
    expect(submitBtn).not.toBeDisabled()
    
    await user.click(submitBtn)

    // Check that button is disabled during loading and shows loading text
    expect(submitBtn).toBeDisabled()
    expect(screen.getByText(/logging in.../i)).toBeInTheDocument()

    // Wait for the request to complete
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled()
    })
  })

  test('makes correct API call with form data', async () => {
    const user = userEvent.setup()
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        user: { id: 1, name: 'Alice' },
        authentication_token: 'abc123' // Corrected token key
      })
    })

    render(<Login />)

    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password$/i), 'password123')
    await user.click(screen.getByRole('button', { name: /login/i }))

    expect(global.fetch).toHaveBeenCalledWith('/api/jobuser/authenticate', { // Corrected endpoint
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })


  test('shows backend validation errors when submission fails', async () => {
    const user = userEvent.setup()
    global.fetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      json: async () => ({ error: { email: 'invalid email format' } }) // Mock backend validation error object
    })

    render(<Login />)

    await user.type(screen.getByLabelText(/email address/i), 'invalid-email')
    await user.type(screen.getByLabelText(/^password$/i), 'password123')
    await user.click(screen.getByRole('button', { name: /login/i }))

    await waitFor(() => {
      // Expecting the specific error message extracted from the error object
      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
    })

    expect(mockLogin).not.toHaveBeenCalled()
    expect(mockNavigate).not.toHaveBeenCalled()
  })
})