// API configuration and service functions
// Use proxy in development, direct URL in production
const API_BASE_URL = import.meta.env.DEV ? '/api' : 'http://localhost:4000';

/**
 * Generic API request function with error handling
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {Object} options - Fetch options
 * @returns {Promise} - Response data or throws error
 */
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData && typeof errorData.error === 'object') { // Check if error is an object
          errorMessage = Object.values(errorData.error).join(', ');
        } else if (response.statusText) {
          errorMessage = response.statusText;
        }
      } catch {
        // If response.json() fails (e.g., empty body), use statusText or generic
        errorMessage = response.statusText || `HTTP error! status: ${response.status}`;
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * User registration API call
 * @param {Object} userData - User registration data
 * @returns {Promise} - Created user data
 */
export const registerUser = async (userData) => {
  return apiRequest('/jobuser/create', {
    method: 'POST',
    body: JSON.stringify(userData),
  });
};

/**
 * User login API call (placeholder for future implementation)
 * @param {Object} credentials - Login credentials
 * @returns {Promise} - User data and token
 */
export const loginUser = async (credentials) => {
  return apiRequest('/jobuser/authenticate', { // Correct endpoint from backend
    method: 'POST',
    body: JSON.stringify(credentials),
  });
};

/**
 * Get user profile API call (placeholder for future implementation)
 * @param {string} userId - User ID
 * @param {string} token - Authentication token
 * @returns {Promise} - User profile data
 */
export const getUserProfile = async (userId, token) => {
  // TODO: Implement when profile endpoint is available
  return apiRequest(`/user/${userId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export default {
  registerUser,
  loginUser,
  getUserProfile,
};
