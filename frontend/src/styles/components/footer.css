/* Footer Component Styles */
.footer {
  background: linear-gradient(to bottom, #2dd4bf, #7c3aed); /* teal-400 to purple-600 */
  color: white;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

/* Footer Header */
.footer-header {
  text-align: center;
  margin-bottom: 2rem;
}

.footer-cta-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.btn-footer-cta {
  background-color: white;
  color: #14b8a6; /* teal-600 */
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn-footer-cta:hover {
  background-color: #f3f4f6; /* gray-100 */
  color: #14b8a6;
  text-decoration: none;
}

/* Footer Grid */
.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 3rem;
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Company Section */
.footer-company {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .footer-company {
    grid-column: span 1;
  }
}

.footer-logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.footer-description {
  font-size: 0.875rem;
  color: #a7f3d0; /* teal-100 */
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 0.75rem;
}

.social-link {
  width: 2rem;
  height: 2rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  text-decoration: none;
}

/* Footer Columns */
.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-column-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  color: #a7f3d0; /* teal-100 */
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: white;
  text-decoration: none;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
  }
}

.footer-copyright {
  color: #a7f3d0; /* teal-100 */
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 1.5rem;
}

.footer-bottom-link {
  color: #a7f3d0; /* teal-100 */
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-link:hover {
  color: white;
  text-decoration: none;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .footer-cta-title {
    font-size: 1.5rem;
  }
  
  .footer-grid {
    gap: 2rem;
  }
  
  .footer-bottom-links {
    gap: 1rem;
  }
}
