/* Form Components */
.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgb(220 38 38 / 0.1);
}

.field-error {
  display: block;
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Error messages */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: var(--error-color);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

/* Success messages */
.success-message {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: var(--success-color);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

/* Responsive form adjustments */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
