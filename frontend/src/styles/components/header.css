/* Header Component Styles */
.header {
  background-color: #193887; /* purple-800 */
  color: white;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 1rem;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: bold;
}

/* Navigation */
.nav {
  display: none;
  align-items: center;
  gap: 2rem;
}

@media (min-width: 768px) {
  .nav {
    display: flex;
  }
}

.nav-link {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #c4b5fd; /* purple-200 */
  text-decoration: none;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.btn-auth {
  color: #193887; /* purple-800 */
  background-color: white;
  border: 1px solid white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn-auth:hover {
  background-color: #faf5ff; /* purple-50 */
  color: #193887;
  text-decoration: none;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .header-content {
    flex-wrap: wrap;
  }
  
  .auth-buttons {
    gap: 0.5rem;
  }
  
  .btn-auth {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}
