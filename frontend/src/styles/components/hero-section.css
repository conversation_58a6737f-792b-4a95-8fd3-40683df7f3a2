/* Hero Section Component Styles */
.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 1rem;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Left Content */
.hero-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #111827; /* gray-900 */
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 3rem;
  }
}

.hero-highlight {
  color: #327567; /* purple-600 */
}

.hero-description {
  color: #6b7280; /* gray-600 */
  font-size: 1.125rem;
}

/* Apply Now Button */
.btn-apply {
  background-color: #193887; /* purple-600 */
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.btn-apply:hover {
  background-color: #193887; /* purple-700 */
  color: white;
  text-decoration: none;
}

.arrow-icon {
  font-size: 1rem;
}

/* Search Form */
.search-form {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.search-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .search-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.search-field {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af; /* gray-400 */
  font-size: 1rem;
  z-index: 10;
}

.search-input,
.search-select {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem;
  font-size: 1rem;
  background-color: white;
}

.search-input:focus,
.search-select:focus {
  outline: none;
  border-color: #193887; /* purple-600 */
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.btn-search {
  background-color: #2563eb; /* blue-600 */
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-search:hover {
  background-color: #1d4ed8; /* blue-700 */
}

/* Right Content - Image */
.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
}

.image-container {
  position: relative;
  z-index: 10;
}

.image-background {
  background: linear-gradient(135deg, #ddd6fe 0%, #dbeafe 100%); /* purple-100 to blue-100 */
  border-radius: 50%;
  padding: 2rem;
  display: inline-block;
}

.hero-main-image {
  width: 20rem;
  height: 20rem;
  object-fit: cover;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-main-image:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.image-decoration {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 18rem;
  height: 18rem;
  background: linear-gradient(135deg, #c7d2fe 0%, #bfdbfe 100%); /* purple-200 to blue-200 */
  border-radius: 50%;
  opacity: 0.5;
  z-index: -10;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-main-image {
    width: 16rem;
    height: 16rem;
  }

  .image-decoration {
    width: 14rem;
    height: 14rem;
  }
}
