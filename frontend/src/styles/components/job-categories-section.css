/* Job Categories Section Component Styles */
.job-categories-section {
  background-color: #ecfeff; /* cyan-100 */
  padding: 3rem 0;
}

.categories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #a5f3fc; /* cyan-200 */
}

.tab-button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6b7280; /* gray-600 */
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-button:hover {
  color: #7c3aed; /* purple-600 */
}

.tab-button.active {
  color: #7c3aed; /* purple-600 */
  border-bottom-color: #7c3aed; /* purple-600 */
}

.tab-icon {
  font-size: 1rem;
}

/* Tab Content */
.tab-content {
  min-height: 12.5rem;
}

/* Department Grid */
.department-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .department-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .department-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.department-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.department-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.department-title {
  font-weight: 600;
  color: #111827; /* gray-900 */
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.job-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.job-link {
  color: #6b7280; /* gray-600 */
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.job-link:hover {
  color: #7c3aed; /* purple-600 */
  text-decoration: none;
}

/* Location Grid */
.location-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .location-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .location-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.location-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  text-align: center;
}

.location-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.location-icon {
  width: 3rem;
  height: 3rem;
  background-color: #f3e8ff; /* purple-100 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  font-size: 1.5rem;
}

.location-name {
  font-weight: 600;
  color: #111827; /* gray-900 */
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.location-count {
  color: #6b7280; /* gray-600 */
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.btn-location {
  color: #7c3aed; /* purple-600 */
  border: 1px solid #7c3aed; /* purple-600 */
  background-color: transparent;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-location:hover {
  background-color: #faf5ff; /* purple-50 */
  color: #7c3aed;
  text-decoration: none;
}

/* Popular Grid */
.popular-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .popular-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .popular-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.popular-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.popular-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

.popular-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.popular-title {
  font-weight: 600;
  color: #111827; /* gray-900 */
  font-size: 1rem;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-green {
  background-color: #dcfce7; /* green-100 */
  color: #166534; /* green-800 */
}

.badge-orange {
  background-color: #fed7aa; /* orange-100 */
  color: #9a3412; /* orange-800 */
}

.badge-blue {
  background-color: #dbeafe; /* blue-100 */
  color: #1e40af; /* blue-800 */
}

.badge-purple {
  background-color: #f3e8ff; /* purple-100 */
  color: #6b21a8; /* purple-800 */
}

.popular-description {
  color: #6b7280; /* gray-600 */
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.popular-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.popular-openings {
  color: #9ca3af; /* gray-500 */
  font-size: 0.875rem;
}

.btn-popular {
  color: #7c3aed; /* purple-600 */
  border: 1px solid #7c3aed; /* purple-600 */
  background-color: transparent;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-popular:hover {
  background-color: #faf5ff; /* purple-50 */
  color: #7c3aed;
  text-decoration: none;
}
