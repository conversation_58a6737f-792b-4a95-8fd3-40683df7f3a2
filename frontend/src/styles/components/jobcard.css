.job-card {
  position: relative; /* For badge positioning */
  background-color: #fff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  display: flex; /* Use flexbox for layout within card */
  flex-direction: column;
  justify-content: space-between; /* Push button to bottom */
}

.job-card:hover {
  transform: translateY(-5px);
}

.job-badge {
  color: #555; /* Darker text for visibility */
  font-weight: normal;
  font-size: 0.9em;
  margin-bottom: 10px; /* Space between deadline and title */
}

.job-badge.deadline-new {
  color: #007bff; /* Blue */
}

.job-badge.deadline-almost-due {
  color: #28a745; /* Green */
}

.job-badge.deadline-very-due {
  color: #dc3545; /* Red */
}

.job-card h3 {
  color: #000; /* Bold black font for title */
  margin-top: 0; /* No need for margin top as badge is not absolute anymore */
  margin-bottom: 10px;
  font-size: 1.5em; /* Prominently displayed */
  font-weight: bold;
}

.job-meta p {
  color: #666;
  font-size: 0.9em;
  line-height: 1.4;
  margin-bottom: 5px;
}

.job-card .btn-apply-now {
  display: block;
  width: 100%;
  background-color: #007bff; /* Changed to blue */
  color: white;
  padding: 12px 0;
  border-radius: 8px;
  text-align: center;
  text-decoration: none;
  font-weight: bold;
  margin-top: 20px;
  transition: background-color 0.3s ease;
}

.job-card .btn-apply-now:hover {
  background-color: #0056b3; /* Darker blue on hover */
}