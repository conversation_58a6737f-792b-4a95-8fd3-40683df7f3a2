/* Statistics Section Component Styles */
.statistics-section {
  padding: 4rem 0;
  background: linear-gradient(to right, #dbeafe, #ecfeff); /* blue-100 to cyan-100 */
}

.statistics-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.statistics-title {
  font-size: 1.875rem;
  font-weight: bold;
  text-align: center;
  color: #111827; /* gray-900 */
  margin-bottom: 3rem;
}

.statistics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  text-align: center;
}

@media (min-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.statistic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.statistic-value {
  font-size: 3.75rem;
  font-weight: bold;
  color: #7c3aed; /* purple-800 */
  margin-bottom: 0.5rem;
  line-height: 1;
}

.statistic-label {
  font-size: 1.125rem;
  color: #374151; /* gray-700 */
  font-weight: 500;
  margin: 0;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .statistic-value {
    font-size: 3rem;
  }
  
  .statistic-label {
    font-size: 1rem;
  }
}
