/* Suggested Jobs Section Component Styles */
.suggested-jobs-section {
  padding: 4rem 0;
  background-color: white;
}

.suggested-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.suggested-title {
  font-size: 1.875rem;
  font-weight: bold;
  text-align: center;
  color: #111827; /* gray-900 */
  margin-bottom: 3rem;
}

/* Suggested Jobs Grid */
.suggested-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .suggested-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .suggested-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Job Cards */
.job-card {
  color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-card-teal {
  background-color: #14b8a6; /* teal-500 */
}

.job-card-purple {
  background-color: #a855f7; /* purple-500 */
}

.job-card-dark-purple {
  background-color: #7c3aed; /* purple-800 */
}

.job-card-blue {
  background-color: #2563eb; /* blue-600 */
}

.job-card-indigo {
  background-color: #4f46e5; /* indigo-600 */
}

.job-card-green {
  background-color: #059669; /* green-600 */
}

.job-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.job-description {
  font-size: 0.875rem;
  margin: 0;
  opacity: 0.9;
}

.job-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.job-salary {
  background-color: white;
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.job-card-teal .job-salary {
  color: #14b8a6; /* teal-500 */
}

.job-card-purple .job-salary {
  color: #a855f7; /* purple-500 */
}

.job-card-dark-purple .job-salary {
  color: #7c3aed; /* purple-800 */
}

.job-card-blue .job-salary {
  color: #2563eb; /* blue-600 */
}

.job-card-indigo .job-salary {
  color: #4f46e5; /* indigo-600 */
}

.job-card-green .job-salary {
  color: #059669; /* green-600 */
}

.job-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.job-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  background-color: rgba(255, 255, 255, 0.2);
}

.btn-apply-job {
  width: 100%;
  background-color: white;
  padding: 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  text-align: center;
  transition: background-color 0.3s ease;
}

.job-card-teal .btn-apply-job {
  color: #14b8a6; /* teal-500 */
}

.job-card-purple .btn-apply-job {
  color: #a855f7; /* purple-500 */
}

.job-card-dark-purple .btn-apply-job {
  color: #7c3aed; /* purple-800 */
}

.job-card-blue .btn-apply-job {
  color: #2563eb; /* blue-600 */
}

.job-card-indigo .btn-apply-job {
  color: #4f46e5; /* indigo-600 */
}

.job-card-green .btn-apply-job {
  color: #059669; /* green-600 */
}

.btn-apply-job:hover {
  background-color: #f3f4f6; /* gray-100 */
  text-decoration: none;
}

/* View All Container */
.view-all-container {
  text-align: center;
}

.btn-view-all {
  background-color: #7c3aed; /* purple-800 */
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn-view-all:hover {
  background-color: #6d28d9; /* purple-900 */
  color: white;
  text-decoration: none;
}
