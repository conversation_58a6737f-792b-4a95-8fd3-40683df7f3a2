/* Dashboard Page Styles */
.dashboard-container {
  min-height: 100vh;
  background-color: var(--surface-color);
  padding: 2rem;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.dashboard-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.dashboard-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Dashboard navigation */
.dashboard-nav {
  background: white;
  padding: 1rem 2rem;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.dashboard-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 2rem;
}

.dashboard-nav a {
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.dashboard-nav a:hover {
  background-color: var(--surface-color);
  text-decoration: none;
}

.dashboard-nav a.active {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive adjustments for dashboard */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-nav ul {
    flex-direction: column;
    gap: 0.5rem;
  }
}
