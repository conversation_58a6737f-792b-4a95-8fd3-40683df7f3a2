/* Login Page Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
  padding: 2rem;
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.login-header p {
  color: var(--text-secondary);
}

.login-form {
  margin-bottom: 2rem;
}

.login-footer {
  text-align: center;
  color: var(--text-secondary);
}

.login-footer p {
  margin-bottom: 0.5rem;
}

/* Login specific form styles */
.login-form .form-group:last-of-type {
  margin-bottom: 2rem;
}

/* Responsive adjustments for login */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }
  
  .login-card {
    padding: 1.5rem;
  }
}

/* Password input wrapper for toggle button */
.password-input-wrapper {
  position: relative;
}

.password-input-wrapper .form-input {
  padding-right: 3rem;
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.password-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Form validation states */
.form-input:invalid:not(:focus):not(:placeholder-shown) {
  border-color: var(--error-color);
}

.form-input:valid:not(:focus):not(:placeholder-shown) {
  border-color: var(--success-color);
}

/* Loading state for button */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Enhanced error message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: "⚠️";
  font-size: 1rem;
}
