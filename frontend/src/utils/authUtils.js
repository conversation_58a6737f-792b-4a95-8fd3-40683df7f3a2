/**
 * Authentication Utility Functions
 * 
 * This file contains helper functions for working with authentication
 * in conjunction with the AuthContext. These utilities provide common
 * authentication-related functionality that can be reused across components.
 */

/**
 * Check if a JWT token is expired
 * 
 * This function decodes a JWT token and checks if it has expired.
 * Note: This only checks the expiration time and doesn't validate the token signature.
 * 
 * @param {string} token - JWT token to check
 * @returns {boolean} True if token is expired, false otherwise
 */
export const isTokenExpired = (token) => {
  if (!token) return true;

  try {
    // Decode the JWT token (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    
    // Check if token has expiration time
    if (!payload.exp) return false;
    
    // Compare expiration time with current time
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired if we can't parse it
  }
};

/**
 * Get token expiration date
 * 
 * Extracts and returns the expiration date from a JWT token.
 * 
 * @param {string} token - JWT token
 * @returns {Date|null} Expiration date or null if not available
 */
export const getTokenExpirationDate = (token) => {
  if (!token) return null;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    if (!payload.exp) return null;
    
    return new Date(payload.exp * 1000);
  } catch (error) {
    console.error('Error getting token expiration date:', error);
    return null;
  }
};

/**
 * Create authorization header for API requests
 * 
 * Creates the Authorization header value for authenticated API requests.
 * 
 * @param {string} token - Authentication token
 * @param {string} type - Token type (default: 'Bearer')
 * @returns {string} Authorization header value
 */
export const createAuthHeader = (token, type = 'Bearer') => {
  if (!token) return '';
  return `${type} ${token}`;
};

/**
 * Create authenticated fetch options
 * 
 * Creates fetch options object with authentication headers included.
 * 
 * @param {string} token - Authentication token
 * @param {Object} options - Additional fetch options
 * @returns {Object} Fetch options with authentication headers
 */
export const createAuthenticatedFetchOptions = (token, options = {}) => {
  const authOptions = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  if (token) {
    authOptions.headers.Authorization = createAuthHeader(token);
  }

  return authOptions;
};

/**
 * Validate user data structure
 * 
 * Validates that user data contains required fields.
 * 
 * @param {Object} userData - User data to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const validateUserData = (userData) => {
  if (!userData || typeof userData !== 'object') {
    return false;
  }

  // Check for required fields (adjust based on your requirements)
    const requiredFields = ['id', 'email'];

  return requiredFields.every(field =>Object.prototype.hasOwnProperty.call(userData, field) && userData[field]);
};

/**
 * Sanitize user data for storage
 * 
 * Removes sensitive information from user data before storing in localStorage.
 * 
 * @param {Object} userData - Raw user data
 * @returns {Object} Sanitized user data
 */
export const sanitizeUserData = (userData) => {
  if (!userData) return null;

  // Create a copy and remove sensitive fields
  const sanitized = { ...userData };
  
  // Remove sensitive fields that shouldn't be stored in localStorage
  delete sanitized.password;
  delete sanitized.passwordConfirm;
  delete sanitized.tempToken;
  
  return sanitized;
};

/**
 * Format user display name
 * 
 * Creates a display name from user data, falling back to email if name is not available.
 * 
 * @param {Object} user - User data object
 * @returns {string} Formatted display name
 */
export const formatUserDisplayName = (user) => {
  if (!user) return 'Unknown User';
  
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  
  if (user.name) {
    return user.name;
  }
  
  if (user.username) {
    return user.username;
  }
  
  if (user.email) {
    return user.email;
  }
  
  return 'Unknown User';
};

/**
 * Check if user has specific role
 * 
 * Checks if the user has a specific role or permission.
 * 
 * @param {Object} user - User data object
 * @param {string|Array} requiredRole - Required role(s)
 * @returns {boolean} True if user has the required role
 */
export const hasRole = (user, requiredRole) => {
  if (!user || !user.role) return false;
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role);
  }
  
  return user.role === requiredRole;
};

/**
 * Check if user has specific permission
 * 
 * Checks if the user has a specific permission.
 * 
 * @param {Object} user - User data object
 * @param {string} permission - Required permission
 * @returns {boolean} True if user has the permission
 */
export const hasPermission = (user, permission) => {
  if (!user || !user.permissions) return false;
  
  if (Array.isArray(user.permissions)) {
    return user.permissions.includes(permission);
  }
  
  return false;
};

/**
 * Storage keys for authentication data
 * 
 * Centralized storage keys to avoid typos and make it easy to change them.
 */
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'authToken',
  AUTH_USER: 'authUser',
  REMEMBER_ME: 'rememberMe',
};

/**
 * Clear all authentication data from localStorage
 * 
 * Utility function to clear all authentication-related data from localStorage.
 * Useful for logout or when switching users.
 */
export const clearAuthStorage = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
};

/**
 * Get stored authentication data
 * 
 * Safely retrieves and parses authentication data from localStorage.
 * 
 * @returns {Object} Object containing token and user data
 */
export const getStoredAuthData = () => {
  try {
    const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    const userJson = localStorage.getItem(STORAGE_KEYS.AUTH_USER);
    const user = userJson ? JSON.parse(userJson) : null;
    
    return { token, user };
  } catch (error) {
    console.error('Error retrieving stored auth data:', error);
    return { token: null, user: null };
  }
};

/**
 * Store authentication data
 * 
 * Safely stores authentication data in localStorage.
 * 
 * @param {string} token - Authentication token
 * @param {Object} user - User data
 */
export const storeAuthData = (token, user) => {
  try {
    if (token) {
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    }
    
    if (user) {
      const sanitizedUser = sanitizeUserData(user);
      localStorage.setItem(STORAGE_KEYS.AUTH_USER, JSON.stringify(sanitizedUser));
    }
  } catch (error) {
    console.error('Error storing auth data:', error);
  }
};

/**
 * API request wrapper with automatic token handling
 * 
 * Wrapper function for making authenticated API requests.
 * Automatically includes authentication headers and handles token expiration.
 * 
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @param {string} token - Authentication token
 * @returns {Promise} Fetch promise
 */
export const authenticatedFetch = async (url, options = {}, token = null) => {
  // Get token from storage if not provided
  if (!token) {
    const storedData = getStoredAuthData();
    token = storedData.token;
  }

  // Check if token is expired
  if (token && isTokenExpired(token)) {
    throw new Error('Token expired');
  }

  // Create authenticated fetch options
  const authOptions = createAuthenticatedFetchOptions(token, options);

  // Make the request
  const response = await fetch(url, authOptions);

  // Handle 401 Unauthorized responses
  if (response.status === 401) {
    // Clear stored auth data on unauthorized response
    clearAuthStorage();
    throw new Error('Unauthorized - please log in again');
  }

  return response;
};
